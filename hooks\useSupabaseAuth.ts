import { supabase } from '@/lib/supabase';
import { AuthState, LoginCredentials, User } from '@/types/User';
import { useCallback, useEffect, useState } from 'react';

export const useSupabaseAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        if (__DEV__) console.log('🔄 [SUPABASE] Checking for existing session...');

        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ [SUPABASE] Error getting session:', error);
          if (mounted) {
            setAuthState({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: error.message,
            });
          }
          return;
        }

        if (session?.user && mounted) {
          if (__DEV__) console.log('✅ [SUPABASE] Found existing session:', session.user.email);

          // Create simple user object from session data
          const user: User = {
            id: session.user.id,
            username: session.user.email?.split('@')[0] || 'user',
            email: session.user.email || '',
            fullName: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            role: 'admin', // Default role for now
            isActive: true,
            createdAt: new Date(session.user.created_at || Date.now()),
            lastLogin: new Date(),
            permissions: {}, // Default permissions
          };

          if (mounted) {
            setAuthState({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          }
        } else if (mounted) {
          if (__DEV__) console.log('ℹ️ [SUPABASE] No existing session found - LOGIN REQUIRED');
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: 'Failed to initialize authentication',
          });
        }
      }
    };

    // Add delay to prevent rapid state changes
    const timeoutId = setTimeout(initializeAuth, 100);

    return () => {
      mounted = false;
      clearTimeout(timeoutId);
    };
  }, []);

  // Listen for auth state changes
  useEffect(() => {
    if (__DEV__) console.log('🔄 [SUPABASE] Setting up auth state listener...');

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (__DEV__) console.log('🔄 [SUPABASE] Auth state change event:', event, session?.user ? 'user present' : 'no user');

        if (event === 'SIGNED_IN' && session?.user) {
          if (__DEV__) console.log('✅ [SUPABASE] User signed in:', session.user.email);

          // Create simple user object from session data
          const user: User = {
            id: session.user.id,
            username: session.user.email?.split('@')[0] || 'user',
            email: session.user.email || '',
            fullName: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
            role: 'admin', // Default role for now
            isActive: true,
            createdAt: new Date(session.user.created_at || Date.now()),
            lastLogin: new Date(),
            permissions: {}, // Default permissions
          };

          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else if (event === 'SIGNED_OUT') {
          if (__DEV__) console.log('ℹ️ [SUPABASE] User signed out');
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      if (__DEV__) console.log('🔄 [SUPABASE] Attempting login for:', credentials.username);

      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Map username to email for Supabase authentication
      const emailMap: Record<string, string> = {
        'Gencer': '<EMAIL>',
        'Kurt': '<EMAIL>',
        'gencer': '<EMAIL>',
        'kurt': '<EMAIL>',
        'test': '<EMAIL>',
        'Test': '<EMAIL>'
      };

      // Check if input is already an email or needs mapping
      if (__DEV__) console.log('🔄 [SUPABASE] Input analysis:', {
        username: credentials.username,
        includesAt: credentials.username.includes('@'),
        length: credentials.username.length
      });

      let email: string;
      if (credentials.username.includes('@')) {
        // If it's already an email, use it directly
        email = credentials.username.trim().toLowerCase();
        if (__DEV__) console.log('✅ [SUPABASE] Using direct email:', email);
      } else {
        // Map username to email
        email = emailMap[credentials.username]?.trim();
        if (__DEV__) console.log('🔄 [SUPABASE] Mapped username to email:', { username: credentials.username, email });
      }

      const password = credentials.password?.trim();

      if (!email) {
        console.error('❌ [SUPABASE] No email found for input:', credentials.username);
        console.error('❌ [SUPABASE] Available mappings:', Object.keys(emailMap));
        console.error('❌ [SUPABASE] Email mapping result:', email);
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Invalid credentials',
        }));
        return false;
      }

      if (!password) {
        console.error('❌ [SUPABASE] Empty password');
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Password is required',
        }));
        return false;
      }

      if (__DEV__) console.log('🔄 [SUPABASE] Attempting signInWithPassword:', {
        email,
        passwordLength: password.length,
        supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL?.substring(0, 50) + '...',
        hasSupabaseUrl: !!process.env.EXPO_PUBLIC_SUPABASE_URL,
        hasSupabaseKey: !!process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
      });

      // Sign in with Supabase Auth - NO LOCAL CHECKS
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ [SUPABASE] Auth error:', {
          message: error.message,
          status: error.status,
          name: error.name,
          details: error
        });
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: error.message,
        }));
        return false;
      }

      if (!data.user) {
        console.error('❌ [SUPABASE] No user data returned');
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Authentication failed',
        }));
        return false;
      }

      if (__DEV__) console.log('✅ [SUPABASE] Login successful:', {
        email: data.user.email,
        id: data.user.id,
        confirmed: data.user.email_confirmed_at ? 'yes' : 'no',
        session: data.session ? 'present' : 'missing'
      });

      // Auth state will be updated by the onAuthStateChange listener
      return true;
    } catch (error: any) {
      console.error('❌ [SUPABASE] Login error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Login failed. Please try again.',
      }));
      return false;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      if (__DEV__) console.log('🔄 Logging out...');

      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Logout error:', error);
      } else {
        if (__DEV__) console.log('✅ Logout successful');
      }
      // Auth state will be updated by the onAuthStateChange listener
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, []);

  return {
    ...authState,
    login,
    logout,
  };
};
