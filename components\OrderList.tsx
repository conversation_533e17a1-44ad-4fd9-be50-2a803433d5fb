import { SwipeableRow } from '@/components/SwipeableRow';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Order } from '@/types/Order';
import { useState } from 'react';
import { Alert, FlatList, Modal, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

interface OrderListProps {
  orders: Order[];
  onUpdateOrderStatus: (orderId: string, status: Order['status'], salePrice?: number) => Promise<boolean>;
  onDeleteOrder: (orderId: string) => Promise<boolean>;
  scrollEnabled?: boolean;
}

export function OrderList({ orders, onUpdateOrderStatus, onDeleteOrder, scrollEnabled = true }: OrderListProps) {
  const [showPriceModal, setShowPriceModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [salePrice, setSalePrice] = useState('');

  // Sort orders: pending first, then cancelled, completed last
  const sortedOrders = [...orders].sort((a, b) => {
    const statusPriority = {
      'pending': 1,
      'cancelled': 2,
      'completed': 3
    };

    const priorityA = statusPriority[a.status];
    const priorityB = statusPriority[b.status];

    // If same status, sort by creation date (newest first)
    if (priorityA === priorityB) {
      const aDate = a.createdAt || a.orderDate;
      const bDate = b.createdAt || b.orderDate;
      return new Date(bDate).getTime() - new Date(aDate).getTime();
    }

    return priorityA - priorityB;
  });

  const borderColor = useThemeColor({ light: '#eee', dark: '#333' }, 'text');
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return '#ff9500';
      case 'completed':
        return '#34c759';
      case 'cancelled':
        return '#ff3b30';
      default:
        return '#8e8e93';
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'completed':
        return '✅';
      case 'cancelled':
        return '❌';
      default:
        return '❓';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'Beklemede';
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return 'Bilinmiyor';
    }
  };

  const handleStatusChange = (order: Order) => {
    setSelectedOrder(order);
    setShowStatusModal(true);
  };

  const handleStatusSelect = async (status: Order['status']) => {
    if (!selectedOrder) return;

    setShowStatusModal(false);

    if (status === 'completed') {
      // Show price modal for completed orders
      setSalePrice('');
      setShowPriceModal(true);
    } else {
      // Update status directly for other statuses
      console.log('🔄 OrderList: Updating order status', {
        orderId: selectedOrder.id,
        newStatus: status,
        orderData: selectedOrder
      });

      const success = await onUpdateOrderStatus(selectedOrder.id, status);
      if (success) {
        console.log('✅ OrderList: Order status updated successfully');
        setSelectedOrder(null);
      } else {
        console.error('❌ OrderList: Failed to update order status');
        Alert.alert('Hata', 'Sipariş durumu güncellenemedi');
      }
    }
  };

  const handleCompleteSale = async () => {
    if (!selectedOrder) return;

    const price = parseFloat(salePrice);
    if (isNaN(price) || price <= 0) {
      Alert.alert('Hata', 'Geçerli bir satış fiyatı giriniz');
      return;
    }

    try {
      console.log('🔄 OrderList: Completing order with sale price', {
        orderId: selectedOrder.id,
        salePrice: price,
        orderData: selectedOrder
      });

      const success = await onUpdateOrderStatus(selectedOrder.id, 'completed', price);
      if (success) {
        console.log('✅ OrderList: Order completed successfully with sale price');
        setShowPriceModal(false);
        setSelectedOrder(null);
        setSalePrice('');
        Alert.alert('Başarılı', 'Sipariş tamamlandı ve satış fiyatı kaydedildi');
      } else {
        console.error('❌ OrderList: Failed to complete order');
        Alert.alert('Hata', 'Sipariş tamamlanamadı');
      }
    } catch (error) {
      Alert.alert('Hata', 'Sipariş güncellenirken bir hata oluştu');
    }
  };

  const handleDeleteOrder = (order: Order) => {
    Alert.alert(
      'Siparişi Sil',
      `"${order.productName}" siparişini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive', 
          onPress: () => onDeleteOrder(order.id) 
        },
      ]
    );
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderOrder = ({ item }: { item: Order }) => {
    // Define swipe actions based on order status
    const getSwipeActions = () => {
      const leftActions = [];
      const rightActions = [];

      if (item.status === 'pending') {
        // Left swipe: Complete order
        leftActions.push({
          text: 'Tamamla',
          backgroundColor: '#34C759',
          icon: '✅',
          onPress: async () => {
            if (item.salePrice) {
              console.log('🔄 OrderList: Quick completing order with existing sale price', {
                orderId: item.id,
                salePrice: item.salePrice,
                orderData: item
              });

              const success = await onUpdateOrderStatus(item.id, 'completed', item.salePrice);
              if (!success) {
                console.error('❌ OrderList: Failed to quick complete order');
                Alert.alert('Hata', 'Sipariş tamamlanamadı');
              }
            } else {
              handleStatusChange(item);
            }
          },
        });

        // Right swipe: Cancel order
        rightActions.push({
          text: 'İptal',
          backgroundColor: '#FF3B30',
          icon: '❌',
          onPress: async () => {
            console.log('🔄 OrderList: Cancelling order', {
              orderId: item.id,
              orderData: item
            });

            const success = await onUpdateOrderStatus(item.id, 'cancelled');
            if (!success) {
              console.error('❌ OrderList: Failed to cancel order');
              Alert.alert('Hata', 'Sipariş iptal edilemedi');
            }
          },
        });
      }

      return { leftActions, rightActions };
    };

    const { leftActions, rightActions } = getSwipeActions();

    return (
      <SwipeableRow
        leftActions={leftActions}
        rightActions={rightActions}
      >
        <ThemedView style={[styles.orderItem, { borderColor }]}>
      <ThemedView style={styles.orderHeader}>
        <ThemedView style={styles.orderInfo}>
          <ThemedText type="defaultSemiBold" style={styles.productName}>
            {item.productName}
          </ThemedText>
          <ThemedText style={styles.customerName}>
            Müşteri: {item.customerName}
          </ThemedText>
        </ThemedView>
        <TouchableOpacity
          style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
          onPress={() => handleStatusChange(item)}
        >
          <ThemedText style={styles.statusIcon}>
            {getStatusIcon(item.status)}
          </ThemedText>
          <ThemedText style={styles.statusText}>
            {getStatusText(item.status)}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ThemedView style={styles.orderDetails}>
        <ThemedText style={styles.quantity}>
          Miktar: {item.quantity} adet
        </ThemedText>
        <ThemedText style={styles.date}>
          Tarih: {formatDate(item.orderDate)}
        </ThemedText>
        {item.salePrice && (
          <ThemedText style={styles.salePrice}>
            Satış Fiyatı: {item.salePrice} ₺
          </ThemedText>
        )}
      </ThemedView>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteOrder(item)}
      >
        <ThemedText style={styles.deleteButtonText}>Sil</ThemedText>
      </TouchableOpacity>
    </ThemedView>
      </SwipeableRow>
    );
  };

  const renderEmptyComponent = () => (
    <ThemedView style={styles.emptyContainer}>
      <ThemedText style={styles.emptyText}>
        Henüz sipariş bulunmuyor
      </ThemedText>
    </ThemedView>
  );

  const renderHeader = () => (
    <ThemedText type="subtitle" style={styles.title}>
      Siparişler ({orders.length})
    </ThemedText>
  );

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={sortedOrders}
        renderItem={renderOrder}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        scrollEnabled={scrollEnabled}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={sortedOrders.length === 0 ? styles.emptyList : styles.listContent}
        style={styles.flatList} // ENSURE FULL HEIGHT
      />

      <Modal
        visible={showPriceModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPriceModal(false)}
      >
        <ThemedView style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { backgroundColor }]}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              Satış Fiyatı Girin
            </ThemedText>

            <ThemedText style={styles.modalSubtitle}>
              {selectedOrder?.productName} - {selectedOrder?.customerName}
            </ThemedText>

            <TextInput
              style={[
                styles.priceInput,
                {
                  color: textColor,
                  backgroundColor: backgroundColor,
                  borderColor: borderColor
                }
              ]}
              value={salePrice}
              onChangeText={setSalePrice}
              placeholder="Satış fiyatını giriniz (₺)"
              placeholderTextColor={borderColor}
              keyboardType="numeric"
              autoFocus
            />

            <ThemedView style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowPriceModal(false)}
              >
                <ThemedText style={styles.cancelButtonText}>İptal</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleCompleteSale}
              >
                <ThemedText style={styles.confirmButtonText}>Tamamla</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </Modal>

      {/* Status Selection Modal */}
      <Modal
        visible={showStatusModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStatusModal(false)}
      >
        <ThemedView style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { backgroundColor }]}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              Sipariş Durumunu Değiştir
            </ThemedText>

            <ThemedText style={styles.modalSubtitle}>
              {selectedOrder?.productName} - {selectedOrder?.customerName}
            </ThemedText>

            <ThemedView style={styles.statusOptions}>
              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#ff9500' }]}
                onPress={() => handleStatusSelect('pending')}
              >
                <ThemedText style={styles.statusOptionText}>Beklemede</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#34c759' }]}
                onPress={() => handleStatusSelect('completed')}
              >
                <ThemedText style={styles.statusOptionText}>Tamamlandı</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#ff3b30' }]}
                onPress={() => handleStatusSelect('cancelled')}
              >
                <ThemedText style={styles.statusOptionText}>İptal Edildi</ThemedText>
              </TouchableOpacity>
            </ThemedView>

            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowStatusModal(false)}
            >
              <ThemedText style={styles.cancelButtonText}>İptal</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    marginBottom: -30, // Android: Prevent bottom overlay
  },
  flatList: {
    flex: 1, // ENSURE FLATLIST TAKES FULL HEIGHT
  },
  title: {
    marginBottom: 15,
    textAlign: 'center',
  },
  orderItem: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  orderInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    marginBottom: 4,
  },
  customerName: {
    fontSize: 14,
    opacity: 0.8,
  },
  statusBadge: {
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 15,
    minWidth: 100,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  statusIcon: {
    color: '#fff',
    fontSize: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  orderDetails: {
    marginBottom: 10,
  },
  quantity: {
    fontSize: 14,
    marginBottom: 4,
  },
  date: {
    fontSize: 12,
    opacity: 0.7,
  },
  salePrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#34c759',
    marginTop: 4,
  },
  deleteButton: {
    backgroundColor: '#ff3b30',
    padding: 8,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    minHeight: 200, // MINIMUM HEIGHT FOR EMPTY STATE
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  emptyList: {
    flexGrow: 1, // GROW TO FILL AVAILABLE SPACE
    justifyContent: 'center', // CENTER EMPTY CONTENT
  },
  listContent: {
    paddingBottom: 0, // Android: Remove bottom padding
    flexGrow: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'auto',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 10,
  },
  modalSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  priceInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#ff3b30',
  },
  confirmButton: {
    backgroundColor: '#34c759',
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  statusOptions: {
    gap: 10,
    marginBottom: 20,
  },
  statusOption: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusOptionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
