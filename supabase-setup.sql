-- Supabase Database Setup Script
-- Bu script'i Supabase SQL Editor'da çalıştırın

-- 1. Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
  role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE,
  permissions JSONB DEFAULT '{}'::jsonb
);

-- 2. Products table
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  stock INTEGER DEFAULT 0,
  description TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID,
  product_name VARCHAR(255) NOT NULL,
  customer_name VARCHAR(255) NOT NULL,
  quantity INTEGER NOT NULL,
  order_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
  sale_price DECIMAL(10,2),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
);

-- 4. Costs table
CREATE TABLE IF NOT EXISTS costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_name VARCHAR(255) NOT NULL,
  quantity INTEGER NOT NULL,
  essence_price DECIMAL(10,2) NOT NULL,
  essence_amount DECIMAL(10,2) NOT NULL,
  essence_used DECIMAL(10,2) NOT NULL,
  alcohol_price DECIMAL(10,2) NOT NULL,
  alcohol_used DECIMAL(10,2) NOT NULL,
  bottle_price DECIMAL(10,2) NOT NULL,
  unit_cost DECIMAL(10,2) NOT NULL,
  total_cost DECIMAL(10,2) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. First create auth users, then insert profile data
-- Note: You need to create auth users manually in Supabase Dashboard first!

-- Function to create user profile after auth user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, username, full_name, role, permissions)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'user')::text,
    COALESCE(NEW.raw_user_meta_data->>'permissions', '{}')::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert admin users manually (after creating auth users)
-- This will be done via the trigger when auth users are created

-- 6. Create auth users for Supabase Auth
-- Bu kısmı manuel olarak Supabase Auth'da yapmanız gerekiyor:
-- 1. Supabase Dashboard > Authentication > Users
-- 2. "Add user" butonuna tıklayın
-- 3. İlk kullanıcı:
--    Email: <EMAIL>
--    Password: Gencer103
--    User Metadata (JSON):
--    {
--      "username": "Gencer",
--      "full_name": "Ana Yönetici - Gencer",
--      "role": "admin",
--      "permissions": {
--        "canManageProducts": true,
--        "canManageOrders": true,
--        "canManageCosts": true,
--        "canViewReports": true,
--        "canManageUsers": true,
--        "canAddProducts": true,
--        "canEditProducts": true,
--        "canDeleteProducts": true,
--        "canCreateOrders": true,
--        "canUpdateOrderStatus": true,
--        "canDeleteOrders": true,
--        "canAddCosts": true,
--        "canEditCosts": true,
--        "canDeleteCosts": true,
--        "canViewFinancials": true,
--        "canExportReports": true,
--        "canViewUserActivity": true,
--        "canChangeUserRoles": true
--      }
--    }
-- 4. İkinci kullanıcı:
--    Email: <EMAIL>
--    Password: Kurt123
--    User Metadata (JSON):
--    {
--      "username": "Kurt",
--      "full_name": "İkinci Yönetici - Kurt",
--      "role": "admin",
--      "permissions": {
--        "canManageProducts": true,
--        "canManageOrders": true,
--        "canManageCosts": true,
--        "canViewReports": true,
--        "canManageUsers": false,
--        "canAddProducts": true,
--        "canEditProducts": true,
--        "canDeleteProducts": true,
--        "canCreateOrders": true,
--        "canUpdateOrderStatus": true,
--        "canDeleteOrders": true,
--        "canAddCosts": true,
--        "canEditCosts": true,
--        "canDeleteCosts": true,
--        "canViewFinancials": true,
--        "canExportReports": true,
--        "canViewUserActivity": false,
--        "canChangeUserRoles": false
--      }
--    }

-- 7. Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE costs ENABLE ROW LEVEL SECURITY;

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON users
  FOR SELECT USING (auth.uid()::uuid = id);

-- Admins can read all users (simplified for now)
CREATE POLICY "Admins can read all users" ON users
  FOR SELECT USING (auth.role() = 'authenticated');

-- Users can manage their own products
CREATE POLICY "Users can view own products" ON products FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own products" ON products FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own products" ON products FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own products" ON products FOR DELETE USING (auth.uid() = user_id);

-- Users can manage their own orders
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own orders" ON orders FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own orders" ON orders FOR DELETE USING (auth.uid() = user_id);

-- Users can manage their own costs
CREATE POLICY "Users can view own costs" ON costs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own costs" ON costs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own costs" ON costs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own costs" ON costs FOR DELETE USING (auth.uid() = user_id);

-- 8. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_costs_product_name ON costs(product_name);
CREATE INDEX IF NOT EXISTS idx_costs_date ON costs(created_at);
