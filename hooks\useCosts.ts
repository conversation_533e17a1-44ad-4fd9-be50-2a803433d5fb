import { Cost, CostSummary } from '@/types/Cost';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

const COSTS_STORAGE_KEY = '@inventory_costs';

export function useCosts() {
  const [costs, setCosts] = useState<Cost[]>([]);
  const [loading, setLoading] = useState(true);

  // Load costs from storage
  useEffect(() => {
    loadCosts();

    // Set up interval to refresh costs every 2 seconds
    const interval = setInterval(() => {
      loadCosts();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const loadCosts = async () => {
    try {
      const storedCosts = await AsyncStorage.getItem(COSTS_STORAGE_KEY);
      if (storedCosts) {
        const parsedCosts = JSON.parse(storedCosts).map((cost: any) => ({
          ...cost,
          createdAt: new Date(cost.createdAt),
        }));
        setCosts(parsedCosts);
      }
    } catch (error) {
      console.error('Error loading costs:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveCosts = async (newCosts: Cost[]) => {
    try {
      await AsyncStorage.setItem(COSTS_STORAGE_KEY, JSON.stringify(newCosts));
      setCosts(newCosts);
    } catch (error) {
      console.error('Error saving costs:', error);
    }
  };

  const addCost = async (
    productName: string,
    quantity: number,
    essencePrice: number,
    essenceAmount: number,
    essenceUsed: number,
    alcoholPrice: number,
    alcoholUsed: number,
    bottlePrice: number
  ) => {
    // Calculate unit cost (for 1 piece)
    const essenceCost = (essencePrice / essenceAmount) * essenceUsed; // Dynamic essence calculation
    const alcoholCost = (alcoholPrice / 1000) * alcoholUsed; // Assuming alcohol price is per 1000ml
    const unitCost = essenceCost + alcoholCost + bottlePrice;
    const totalCost = unitCost * quantity;

    const newCost: Cost = {
      id: Date.now().toString(),
      productName: productName.trim(),
      quantity,
      essencePrice,
      essenceAmount,
      essenceUsed,
      alcoholPrice,
      alcoholUsed,
      bottlePrice,
      unitCost,
      totalCost,
      createdAt: new Date(),
    };

    const updatedCosts = [...costs, newCost];
    await saveCosts(updatedCosts);
  };

  const deleteCost = async (costId: string) => {
    const updatedCosts = costs.filter(cost => cost.id !== costId);
    await saveCosts(updatedCosts);
  };

  const getCostSummary = (): CostSummary => {
    const totalProducts = costs.length;
    const totalCost = costs.reduce((sum, cost) => sum + cost.totalCost, 0);
    const averageCost = totalProducts > 0 ? totalCost / totalProducts : 0;
    const totalEssenceUsed = costs.reduce((sum, cost) => sum + cost.essenceUsed, 0);
    const totalAlcoholUsed = costs.reduce((sum, cost) => sum + cost.alcoholUsed, 0);

    return {
      totalProducts,
      totalCost,
      averageCost,
      totalEssenceUsed,
      totalAlcoholUsed,
    };
  };

  const getCostByProduct = (productName: string) => {
    return costs.filter(cost => 
      cost.productName.toLowerCase().includes(productName.toLowerCase())
    );
  };

  return {
    costs,
    loading,
    addCost,
    deleteCost,
    getCostSummary,
    getCostByProduct,
    refreshCosts: loadCosts,
  };
}
