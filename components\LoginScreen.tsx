import { AnimatedButton } from '@/components/AnimatedButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { usePersistentAuth } from '@/hooks/usePersistentAuth';
import { useThemeColor } from '@/hooks/useThemeColor';
import React, { useState } from 'react';
import {
    Animated,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    StyleSheet,
    TextInput
} from 'react-native';

export function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [localError, setLocalError] = useState<string | null>(null);

  // Fade animation for smooth transition from splash
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  const { login, isLoading, error: authError, isAuthenticated, user } = usePersistentAuth();

  // Debug loading state
  console.log('🖥️ LoginScreen render - isLoading:', isLoading, 'authError:', authError, 'localError:', localError, 'isAuthenticated:', isAuthenticated);

  // Fade-in animation on component mount for smooth transition - LAYOUT SHIFT PREVENTION
  React.useEffect(() => {
    console.log('🎬 LoginScreen mounted - starting smooth fade-in animation');

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300, // Smooth fade-in (300ms) to prevent layout shift
      useNativeDriver: true,
      // No easing - use default for compatibility
    }).start(() => {
      console.log('🎬 LoginScreen fade-in animation completed');
    });
  }, [fadeAnim]);

  // FORCEFUL NAVIGATION - SUPABASE AUTH SUCCESS
  // No navigation needed - tab layout handles authentication flow

  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({ light: '#ddd', dark: '#444' }, 'text');

  const handleLogin = async () => {
    // Clear previous errors
    setLocalError(null);

    if (!username.trim() || !password.trim()) {
      setLocalError('Kullanıcı adı ve şifre gereklidir');
      return;
    }

    console.log('🔐 Yönetici login attempt:', {
      username: username.trim(),
      password: '***',
      usernameLength: username.trim().length,
      passwordLength: password.length
    });

    try {
      const success = await login({ username: username.trim(), password });
      console.log('📊 Yönetici login result:', success);

      if (success) {
        console.log('🎉 Yönetici login successful! Auth state should be updated');
        setLocalError(null); // Clear any errors
        // Navigasyon kodu kaldırıldı - useEffect bunu halledecek
      } else {
        // Check for auth error or set generic error
        const errorMessage = authError || 'Giriş başarısız. Kullanıcı adı ve şifrenizi kontrol edin.';
        console.log('❌ Yönetici login error:', errorMessage);
        setLocalError(errorMessage);
      }
    } catch (err) {
      console.error('💥 Yönetici login exception:', err);
      setLocalError('Giriş başarısız. Kullanıcı adı ve şifrenizi kontrol edin.');
    }
  };



  return (
    <SafeAreaView style={styles.safeArea}>
      <ThemedView style={styles.flexContainer}>
        <KeyboardAvoidingView
          style={styles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        >
          <Animated.View style={[styles.animatedContainer, { opacity: fadeAnim }]}>
            <ThemedView style={styles.content}>
              {/* Logo/Title */}
              <ThemedView style={styles.header}>
                <ThemedText type="title" style={styles.title}>
                  📦 Envanter Yönetimi
                </ThemedText>
                <ThemedText style={styles.subtitle}>
                  Yönetici girişi
                </ThemedText>
              </ThemedView>

              {/* Login Form */}
              <ThemedView style={styles.form}>
                <ThemedView style={styles.inputGroup}>
                  <ThemedText style={styles.label}>Kullanıcı Adı</ThemedText>
                  <TextInput
                    style={[styles.input, { color: textColor, borderColor }]}
                    value={username}
                    onChangeText={(text) => {
                      setUsername(text);
                      if (localError) setLocalError(null); // Clear error when typing
                    }}
                    placeholder="Kullanıcı adınızı girin"
                    placeholderTextColor={textColor + '80'}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </ThemedView>

                <ThemedView style={styles.inputGroup}>
                  <ThemedText style={styles.label}>Şifre</ThemedText>
                  <TextInput
                    style={[styles.input, { color: textColor, borderColor }]}
                    value={password}
                    onChangeText={(text) => {
                      setPassword(text);
                      if (localError) setLocalError(null); // Clear error when typing
                    }}
                    placeholder="Şifrenizi girin"
                    placeholderTextColor={textColor + '80'}
                    secureTextEntry
                  />
                </ThemedView>

                {(localError || authError) && (
                  <ThemedView style={styles.errorContainer}>
                    <ThemedText style={styles.errorText}>{localError || authError}</ThemedText>
                  </ThemedView>
                )}

                <AnimatedButton
                  title={isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
                  onPress={() => {
                    console.log('🔘 Login button pressed, current isLoading:', isLoading);
                    handleLogin();
                  }}
                  disabled={isLoading}
                  variant="primary"
                  size="large"
                  style={styles.loginButton}
                />
              </ThemedView>
            </ThemedView>
          </Animated.View>
        </KeyboardAvoidingView>
      </ThemedView>
    </SafeAreaView>

  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    // backgroundColor will be set dynamically via theme
  },
  container: {
    flex: 1,
    overflow: 'hidden', // Prevent layout shift
  },
  animatedContainer: {
    flex: 1,
    overflow: 'hidden', // Prevent layout shift
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'stretch',
    padding: 16,
    maxWidth: 400,
    alignSelf: 'center',
  },
  animatedContent: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 17,
    opacity: 0.7,
    textAlign: 'center',
    letterSpacing: -0.2,
  },
  form: {
    marginBottom: 24,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
    letterSpacing: -0.2,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 48,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: '#FF3B30',
    textAlign: 'center',
    fontSize: 14,
  },
  loginButton: {
    marginTop: 10,
  },
  flexContainer: {
    flex: 1,
  },

});

