// Sentry is disabled
export function initSentry() {
  // No-op
}

export function captureError(error: Error, context?: Record<string, any>) {
  // No-op
}

export function captureMessage(message: string, level: any = 'info') {
  // No-op
}

export function setUser(user: { id: string; email?: string; username?: string } | null) {
  // No-op
}

export function addBreadcrumb(breadcrumb: any) {
  // No-op
} 