import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet } from 'react-native';

interface LoadingSkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export function LoadingSkeleton({ 
  width = '100%', 
  height = 20, 
  borderRadius = 8,
  style 
}: LoadingSkeletonProps) {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const backgroundColor = useThemeColor({ light: '#f0f0f0', dark: '#2a2a2a' }, 'background');
  const shimmerColor = useThemeColor({ light: '#e0e0e0', dark: '#3a3a3a' }, 'background');

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [animatedValue]);

  const animatedStyle = {
    backgroundColor: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [backgroundColor, shimmerColor],
    }),
  };

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        animatedStyle,
        style,
      ]}
    />
  );
}

// Product Item Skeleton
export function ProductItemSkeleton() {
  return (
    <ThemedView style={styles.productSkeletonContainer}>
      <ThemedView style={styles.productSkeletonContent}>
        <ThemedView style={styles.productSkeletonInfo}>
          <LoadingSkeleton width="70%" height={18} borderRadius={4} style={{ marginBottom: 8 }} />
          <LoadingSkeleton width="50%" height={14} borderRadius={4} style={{ marginBottom: 8 }} />
          <LoadingSkeleton width="100%" height={6} borderRadius={3} style={{ marginBottom: 8 }} />
          <LoadingSkeleton width="40%" height={12} borderRadius={4} />
        </ThemedView>
        <LoadingSkeleton width={48} height={48} borderRadius={12} />
      </ThemedView>
    </ThemedView>
  );
}

// Stat Card Skeleton
export function StatCardSkeleton() {
  return (
    <ThemedView style={styles.statSkeletonContainer}>
      <LoadingSkeleton width={60} height={24} borderRadius={6} style={{ marginBottom: 8 }} />
      <LoadingSkeleton width="80%" height={14} borderRadius={4} />
    </ThemedView>
  );
}

// Form Skeleton
export function FormSkeleton() {
  return (
    <ThemedView style={styles.formSkeletonContainer}>
      <LoadingSkeleton width="100%" height={20} borderRadius={6} style={{ marginBottom: 16 }} />
      <LoadingSkeleton width="100%" height={48} borderRadius={12} style={{ marginBottom: 16 }} />
      <LoadingSkeleton width="100%" height={48} borderRadius={12} style={{ marginBottom: 16 }} />
      <LoadingSkeleton width="100%" height={48} borderRadius={12} style={{ marginBottom: 24 }} />
      <LoadingSkeleton width="100%" height={48} borderRadius={12} />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  skeleton: {
    overflow: 'hidden',
  },
  productSkeletonContainer: {
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
    borderRadius: 16,
    marginBottom: 12,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  productSkeletonContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productSkeletonInfo: {
    flex: 1,
    marginRight: 16,
  },
  statSkeletonContainer: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: 'rgba(0, 122, 255, 0.08)',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  formSkeletonContainer: {
    padding: 24,
    margin: 16,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.1)',
  },
});
