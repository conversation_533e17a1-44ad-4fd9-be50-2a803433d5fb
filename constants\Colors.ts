/**
 * Notion + Apple Settings inspired theme
 * Clean, readable, and elegant design system
 */

const tintColorLight = '#007AFF';
const tintColorDark = '#007AFF';

export const Colors = {
  light: {
    text: '#1A1A1A',
    textSecondary: '#8E8E93',
    background: '#F5F5F5',
    surface: '#FFFFFF',
    border: '#E5E5E5',
    primary: tintColorLight,
    accent: '#34C759',
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30',
    tint: tintColorLight,
    icon: '#8E8E93',
    tabIconDefault: '#8E8E93',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#FFFFFF',
    textSecondary: '#8E8E93',
    background: '#1A1A1A',
    surface: '#2C2C2E',
    border: '#38383A',
    primary: tintColorDark,
    accent: '#30D158',
    success: '#30D158',
    warning: '#FF9F0A',
    error: '#FF453A',
    tint: tintColorDark,
    icon: '#8E8E93',
    tabIconDefault: '#8E8E93',
    tabIconSelected: tintColorDark,
  },
};
