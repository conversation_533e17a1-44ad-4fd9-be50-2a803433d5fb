import { SplashScreenLogo } from '@/components/SplashScreenLogo';
import { ThemedView } from '@/components/ThemedView';
import { useGlobalSplash } from '@/hooks/useGlobalSplash';
import { useImmersiveMode } from '@/hooks/useImmersiveMode';
import { useSupabaseAuthForced as useAuth } from '@/hooks/useSupabaseAuthForced';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Tabs } from 'expo-router';
import React from 'react';
import { ActivityIndicator, Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  // ALL HOOKS MUST BE AT THE TOP - REACT HOOKS RULES
  const colorScheme = useColorScheme();
  const authState = useAuth();
  const { isAuthenticated, isLoading, user } = authState;
  const { shouldShowSplash, markSplashFinished, debugInfo } = useGlobalSplash();
  const backgroundColor = useThemeColor({}, 'background');
  const [forceRender, setForceRender] = React.useState(0);

  // Immersive mode hook - tüm tab ekranlarında aktif
  const { showSystemBars } = useImmersiveMode({
    hideNavigationBar: true,
    hideStatusBar: true,
    autoHideOnFocus: true,
    enableSwipeGesture: true,
  });

  // Enhanced platform detection
  const isReactNative = Platform.OS === 'ios' || Platform.OS === 'android';
  const isMobile = isReactNative ||
                  typeof window === 'undefined' ||
                  (typeof navigator !== 'undefined' && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator?.userAgent || ''));

  const platformLabel = isReactNative ? '📱 RN' : (isMobile ? '📱 Mobile' : '💻 Web');

  // ALL USEEFFECT HOOKS MUST BE HERE - BEFORE ANY EARLY RETURNS
  // Monitor auth state changes - ENHANCED FOR LOGOUT (prevent white screen)
  React.useEffect(() => {
    if (__DEV__) {
      console.log(`${platformLabel} Auth state changed:`, {
        isAuthenticated,
        isLoading,
        user: user?.username
      });
    }

    // Force re-render on auth state changes - ESPECIALLY FOR LOGOUT
    if (isAuthenticated && !isLoading && user) {
      console.log(`${platformLabel} User authenticated - showing main app`);
      setForceRender(prev => prev + 1);
    } else if (!isAuthenticated && !isLoading && !user) {
      console.log(`${platformLabel} User logged out - showing login screen`);
      setForceRender(prev => prev + 1);
    }
  }, [isAuthenticated, user, isLoading, platformLabel]);

  // No need for navigation retry since we render login screen directly

  // Debug function to reset splash (for testing)
  React.useEffect(() => {
    if (typeof window !== 'undefined' && __DEV__) {
      (window as any).getSplashDebugInfo = () => {
        console.log('🎬 Splash Debug Info:', debugInfo);
        return debugInfo;
      };
    }
  }, [debugInfo]);

  // Debug logging (reduced for production)
  if (__DEV__) {
    console.log(`${platformLabel} TabLayout:`, {
      isAuthenticated,
      isLoading,
      user: user?.username
    });
  }

  // Handle splash screen finish - ALWAYS mark as finished after animation
  const handleSplashFinish = () => {
    if (__DEV__) console.log('🎬 Splash screen animation finished - marking as finished');
    // ALWAYS mark splash finished regardless of auth state
    markSplashFinished();
  };

  // Force splash finish if auth is determined but splash is still showing
  React.useEffect(() => {
    if (!isLoading && shouldShowSplash()) {
      console.log('🎬 Auth determined but splash still showing - forcing finish');
      setTimeout(() => {
        markSplashFinished();
      }, 100);
    }
  }, [isLoading, shouldShowSplash, markSplashFinished]);

  // Emergency splash finish timer - GUARANTEED to finish splash
  React.useEffect(() => {
    const emergencyTimer = setTimeout(() => {
      if (shouldShowSplash()) {
        console.log('🚨 EMERGENCY: Splash screen stuck after 6 seconds - FORCING FINISH');
        markSplashFinished();
      }
    }, 6000); // 6 seconds emergency timer

    return () => clearTimeout(emergencyTimer);
  }, [shouldShowSplash, markSplashFinished]);

  // NOW SAFE TO HAVE EARLY RETURNS - ALL HOOKS ARE ABOVE
  // Show splash screen only if explicitly should show (not based on auth loading)
  if (shouldShowSplash()) {
    return (
      <ThemedView style={{
        flex: 1,
      }}>
        <SplashScreenLogo onAnimationFinish={handleSplashFinish} />
      </ThemedView>
    );
  }

  // Show loading indicator while auth is being determined
  if (isLoading) {
    return (
      <ThemedView style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor,
      }}>
        <ActivityIndicator size="large" color="#007AFF" />
      </ThemedView>
    );
  }

  // PROTECTED SCREENS: Show login screen if not authenticated
  if (!isAuthenticated || !user) {
    // Import LoginScreen component at the top of the file
    const LoginScreen = require('@/components/LoginScreen').LoginScreen;
    const SafeAreaView = require('react-native-safe-area-context').SafeAreaView;
    const StatusBar = require('react-native').StatusBar;

    return (
      <SafeAreaView style={{
        flex: 1,
        backgroundColor,
        ...Platform.select({
          android: {
            paddingTop: StatusBar.currentHeight || 0,
          },
        }),
      }}>
        <ThemedView style={{ flex: 1 }} noBackground>
          <LoginScreen />
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#007AFF', // Aktif sekme mavi
        tabBarInactiveTintColor: '#8E8E93', // Pasif sekmeler gri
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            paddingTop: 8,
            height: 85,
          },
          android: {
            backgroundColor: colorScheme === 'dark' ? '#1A1A1A' : '#F5F5F5',
            borderTopWidth: 1,
            borderTopColor: colorScheme === 'dark' ? '#38383A' : '#E5E5E5',
            paddingTop: 8,
            paddingBottom: 20, // Extra padding for immersive mode
            height: 80, // Increased height for immersive mode
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
          },
          default: {
            backgroundColor: colorScheme === 'dark' ? '#1A1A1A' : '#F5F5F5',
            borderTopWidth: 1,
            borderTopColor: colorScheme === 'dark' ? '#38383A' : '#E5E5E5',
            paddingTop: 8,
            paddingBottom: 8,
            height: 65,
            position: 'relative',
          },
        }),
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Anasayfa',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="products"
        options={{
          title: 'Ürünler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="list.bullet" color={color} />,
        }}
      />
      <Tabs.Screen
        name="orders"
        options={{
          title: 'Siparişler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="paperplane.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="costs"
        options={{
          title: 'Maliyetler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="gear" color={color} />,
        }}
      />

      <Tabs.Screen
        name="reports"
        options={{
          title: 'Raporlar',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="chart.bar" color={color} />,
        }}
      />

      <Tabs.Screen
        name="suppliers"
        options={{
          title: 'Tedarikçiler',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="building.2" color={color} />,
        }}
      />

    </Tabs>
  );
}
