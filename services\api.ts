/**
 * API Service Layer for Backend Communication
 * Handles all HTTP requests and authentication
 */


// API Configuration
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3001/api';
const API_TIMEOUT = 10000; // 10 seconds

// Token management
let authToken: string | null = null;
let refreshToken: string | null = null;

// Token storage keys
const TOKEN_STORAGE_KEY = '@inventory_token';
const REFRESH_TOKEN_STORAGE_KEY = '@inventory_refresh_token';

// Storage interface for cross-platform compatibility
interface Storage {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
}

// Cross-platform storage implementation
const storage: Storage = {
  async getItem(key: string): Promise<string | null> {
    if (typeof window !== 'undefined') {
      // Web platform
      try {
        return localStorage.getItem(key) || sessionStorage.getItem(key);
      } catch {
        return null;
      }
    } else {
      // React Native platform
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      return await AsyncStorage.getItem(key);
    }
  },

  async setItem(key: string, value: string): Promise<void> {
    if (typeof window !== 'undefined') {
      // Web platform
      try {
        localStorage.setItem(key, value);
        sessionStorage.setItem(key, value);
      } catch {
        // Ignore storage errors
      }
    } else {
      // React Native platform
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.setItem(key, value);
    }
  },

  async removeItem(key: string): Promise<void> {
    if (typeof window !== 'undefined') {
      // Web platform
      try {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      } catch {
        // Ignore storage errors
      }
    } else {
      // React Native platform
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.removeItem(key);
    }
  },
};

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// HTTP Client with authentication
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string, timeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorCode = response.status.toString();

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
        errorCode = errorData.code || errorCode;
      } catch {
        // Use default error message if JSON parsing fails
      }

      // Handle token expiration
      if (response.status === 401) {
        await this.handleTokenExpiration();
      }

      throw new ApiError(errorMessage, response.status, errorCode);
    }

    try {
      return await response.json();
    } catch {
      throw new ApiError('Invalid JSON response');
    }
  }

  private async handleTokenExpiration(): Promise<void> {
    // Clear tokens
    authToken = null;
    await storage.removeItem(TOKEN_STORAGE_KEY);
    await storage.removeItem(REFRESH_TOKEN_STORAGE_KEY);

    // Redirect to login or emit logout event (web only)
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      try {
        window.dispatchEvent(new CustomEvent('auth:logout'));
      } catch (error) {
        console.warn('Failed to dispatch logout event:', error);
      }
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers: await this.getAuthHeaders(),
        signal: controller.signal,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  async delete<T>(endpoint: string): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers: await this.getAuthHeaders(),
        signal: controller.signal,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }
}

// Create API client instance
const apiClient = new ApiClient(API_BASE_URL);

// Token management functions
export const tokenManager = {
  async loadTokens(): Promise<void> {
    try {
      authToken = await storage.getItem(TOKEN_STORAGE_KEY);
      refreshToken = await storage.getItem(REFRESH_TOKEN_STORAGE_KEY);
    } catch (error) {
      console.error('Error loading tokens:', error);
    }
  },

  async saveTokens(token: string, refresh: string): Promise<void> {
    try {
      authToken = token;
      refreshToken = refresh;
      await storage.setItem(TOKEN_STORAGE_KEY, token);
      await storage.setItem(REFRESH_TOKEN_STORAGE_KEY, refresh);
    } catch (error) {
      console.error('Error saving tokens:', error);
    }
  },

  async clearTokens(): Promise<void> {
    try {
      authToken = null;
      refreshToken = null;
      await storage.removeItem(TOKEN_STORAGE_KEY);
      await storage.removeItem(REFRESH_TOKEN_STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  },

  getToken(): string | null {
    return authToken;
  },

  getRefreshToken(): string | null {
    return refreshToken;
  },
};

// Export API client
export { apiClient };

