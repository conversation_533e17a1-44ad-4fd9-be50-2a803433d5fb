import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Product } from '@/types/Product';
import React, { useState } from 'react';
import { Alert, FlatList, Modal, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

interface CostFormProps {
  products: Product[];
  onAddCost: (
    productName: string,
    quantity: number,
    essencePrice: number,
    essenceAmount: number,
    essenceUsed: number,
    alcoholPrice: number,
    alcoholUsed: number,
    bottlePrice: number
  ) => Promise<void>;
}

export function CostForm({ onAddCost, products }: CostFormProps) {
  const [productName, setProductName] = useState('');
  const [showProductPicker, setShowProductPicker] = useState(false);
  const [quantity, setQuantity] = useState('1');
  const [essencePrice, setEssencePrice] = useState('');
  const [essenceAmount, setEssenceAmount] = useState('');
  const [essenceUsed, setEssenceUsed] = useState('');
  const [alcoholPrice, setAlcoholPrice] = useState('');
  const [alcoholUsed, setAlcoholUsed] = useState('');
  const [bottlePrice, setBottlePrice] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Debug: Log products (only in development)
  if (__DEV__) console.log('CostForm - Products received:', products.length, products);

  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'surface');
  const borderColor = useThemeColor({}, 'border');
  const primaryColor = useThemeColor({}, 'primary');
  const errorColor = useThemeColor({}, 'error');

  const calculatePreview = () => {
    const qty = parseFloat(quantity) || 1;
    const essence = parseFloat(essencePrice) || 0;
    const essenceBottleAmount = parseFloat(essenceAmount) || 1; // Prevent division by zero
    const essenceUsedAmount = parseFloat(essenceUsed) || 0;
    const alcohol = parseFloat(alcoholPrice) || 0;
    const alcoholAmount = parseFloat(alcoholUsed) || 0;
    const bottle = parseFloat(bottlePrice) || 0;

    const essenceCost = (essence / essenceBottleAmount) * essenceUsedAmount;
    const alcoholCost = (alcohol / 1000) * alcoholAmount;
    const unitCost = essenceCost + alcoholCost + bottle;
    const total = unitCost * qty;

    return {
      essenceCost,
      alcoholCost,
      bottleCost: bottle,
      unitCost,
      quantity: qty,
      total,
    };
  };

  const handleSubmit = async () => {
    if (!productName.trim()) {
      Alert.alert('Hata', 'Lütfen ürün adını girin');
      return;
    }

    const qty = parseFloat(quantity);
    const essence = parseFloat(essencePrice);
    const essenceBottleAmount = parseFloat(essenceAmount);
    const essenceUsedAmount = parseFloat(essenceUsed);
    const alcohol = parseFloat(alcoholPrice);
    const alcoholAmount = parseFloat(alcoholUsed);
    const bottle = parseFloat(bottlePrice);

    if (isNaN(qty) || qty <= 0) {
      Alert.alert('Hata', 'Geçerli bir adet sayısı giriniz');
      return;
    }

    if (isNaN(essence) || essence < 0) {
      Alert.alert('Hata', 'Geçerli bir esans fiyatı giriniz');
      return;
    }

    if (isNaN(essenceBottleAmount) || essenceBottleAmount <= 0) {
      Alert.alert('Hata', 'Geçerli bir esans şişe miktarı giriniz');
      return;
    }

    if (isNaN(essenceUsedAmount) || essenceUsedAmount < 0) {
      Alert.alert('Hata', 'Geçerli bir kullanılan esans miktarı giriniz');
      return;
    }

    if (isNaN(alcohol) || alcohol < 0) {
      Alert.alert('Hata', 'Geçerli bir alkol fiyatı giriniz');
      return;
    }

    if (isNaN(alcoholAmount) || alcoholAmount < 0) {
      Alert.alert('Hata', 'Geçerli bir alkol miktarı giriniz');
      return;
    }

    if (isNaN(bottle) || bottle < 0) {
      Alert.alert('Hata', 'Geçerli bir şişe fiyatı giriniz');
      return;
    }

    setIsSubmitting(true);
    try {
      await onAddCost(
        productName.trim(),
        qty,
        essence,
        essenceBottleAmount,
        essenceUsedAmount,
        alcohol,
        alcoholAmount,
        bottle
      );
      setProductName('');
      setQuantity('1');
      setEssencePrice('');
      setEssenceAmount('');
      setEssenceUsed('');
      setAlcoholPrice('');
      setAlcoholUsed('');
      setBottlePrice('');
      Alert.alert('Başarılı', 'Maliyet kaydı başarıyla eklendi');
    } catch (error) {
      console.error('Error adding cost:', error);
      Alert.alert('Hata', 'Maliyet kaydı eklenirken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  const preview = calculatePreview();

  return (
    <ScrollView
      style={styles.scrollContainer}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      <ThemedView style={styles.container}>
        <ThemedText type="subtitle" style={styles.title}>
          Maliyet Hesaplama
        </ThemedText>
      
        <ThemedView style={styles.formGroup}>
          <ThemedText style={styles.label}>Ürün Adı</ThemedText>
          <TouchableOpacity
            style={[styles.pickerButton, { borderColor, backgroundColor }]}
            onPress={() => setShowProductPicker(true)}
            disabled={isSubmitting}
          >
            <ThemedText style={[styles.pickerButtonText, { color: productName ? textColor : borderColor }]}>
              {productName || 'Ürün seçiniz...'}
            </ThemedText>
            <ThemedText style={[styles.pickerArrow, { color: textColor }]}>▼</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        <ThemedView style={styles.formGroup}>
          <ThemedText style={styles.label}>Adet Sayısı</ThemedText>
          <TextInput
            style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
            value={quantity}
            onChangeText={setQuantity}
            placeholder="1"
            placeholderTextColor={borderColor}
            keyboardType="numeric"
            editable={!isSubmitting}
          />
        </ThemedView>

        <ThemedView style={styles.row}>
          <ThemedView style={styles.halfWidth}>
            <ThemedText style={styles.label}>Esans Fiyatı (₺)</ThemedText>
            <TextInput
              style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
              value={essencePrice}
              onChangeText={setEssencePrice}
              placeholder="0"
              placeholderTextColor={borderColor}
              keyboardType="numeric"
              editable={!isSubmitting}
            />
          </ThemedView>

          <ThemedView style={styles.halfWidth}>
            <ThemedText style={styles.label}>Esans Şişe Miktarı (ml)</ThemedText>
            <TextInput
              style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
              value={essenceAmount}
              onChangeText={setEssenceAmount}
              placeholder="100"
              placeholderTextColor={borderColor}
              keyboardType="numeric"
              editable={!isSubmitting}
            />
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.formGroup}>
          <ThemedText style={styles.label}>Kullanılan Esans (ml)</ThemedText>
          <TextInput
            style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
            value={essenceUsed}
            onChangeText={setEssenceUsed}
            placeholder="0"
            placeholderTextColor={borderColor}
            keyboardType="numeric"
            editable={!isSubmitting}
          />
        </ThemedView>

        <ThemedView style={styles.row}>
          <ThemedView style={styles.halfWidth}>
            <ThemedText style={styles.label}>Alkol Fiyatı (₺/1000ml)</ThemedText>
            <TextInput
              style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
              value={alcoholPrice}
              onChangeText={setAlcoholPrice}
              placeholder="0"
              placeholderTextColor={borderColor}
              keyboardType="numeric"
              editable={!isSubmitting}
            />
          </ThemedView>

          <ThemedView style={styles.halfWidth}>
            <ThemedText style={styles.label}>Kullanılan Alkol (ml)</ThemedText>
            <TextInput
              style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
              value={alcoholUsed}
              onChangeText={setAlcoholUsed}
              placeholder="0"
              placeholderTextColor={borderColor}
              keyboardType="numeric"
              editable={!isSubmitting}
            />
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.formGroup}>
          <ThemedText style={styles.label}>Şişe Fiyatı (₺)</ThemedText>
          <TextInput
            style={[styles.input, { color: textColor, backgroundColor: backgroundColor, borderColor: borderColor }]}
            value={bottlePrice}
            onChangeText={setBottlePrice}
            placeholder="0"
            placeholderTextColor={borderColor}
            keyboardType="numeric"
            editable={!isSubmitting}
          />
        </ThemedView>

        {/* Cost Preview */}
        <ThemedView style={styles.previewContainer}>
          <ThemedText type="defaultSemiBold" style={styles.previewTitle}>
            Maliyet Önizleme
          </ThemedText>
          <ThemedView style={styles.previewRow}>
            <ThemedText style={styles.previewLabel}>Esans Maliyeti:</ThemedText>
            <ThemedText style={styles.previewValue}>{preview.essenceCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={styles.previewRow}>
            <ThemedText style={styles.previewLabel}>Alkol Maliyeti:</ThemedText>
            <ThemedText style={styles.previewValue}>{preview.alcoholCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={styles.previewRow}>
            <ThemedText style={styles.previewLabel}>Şişe Maliyeti:</ThemedText>
            <ThemedText style={styles.previewValue}>{preview.bottleCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={[styles.previewRow, styles.unitRow]}>
            <ThemedText style={styles.unitLabel}>BİRİM MALİYET (1 adet):</ThemedText>
            <ThemedText style={styles.unitValue}>{preview.unitCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={styles.previewRow}>
            <ThemedText style={styles.previewLabel}>Adet Sayısı:</ThemedText>
            <ThemedText style={styles.previewValue}>{preview.quantity}</ThemedText>
          </ThemedView>
          <ThemedView style={[styles.previewRow, styles.totalRow]}>
            <ThemedText style={styles.totalLabel}>TOPLAM MALİYET:</ThemedText>
            <ThemedText style={styles.totalValue}>{preview.total.toFixed(2)} ₺</ThemedText>
          </ThemedView>
        </ThemedView>

        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: primaryColor },
            isSubmitting && styles.submitButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <ThemedText style={styles.submitButtonText}>
            {isSubmitting ? 'Kaydediliyor...' : 'Maliyet Kaydı Ekle'}
          </ThemedText>
        </TouchableOpacity>

        {/* Product Picker Modal */}
        <Modal
          visible={showProductPicker}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowProductPicker(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowProductPicker(false)}
          >
            <ThemedView style={[styles.modalContent, { backgroundColor }]}>
              <ThemedText style={styles.modalTitle}>Ürün Seçin</ThemedText>

              <FlatList
                data={products}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.productItem}
                    onPress={() => {
                      setProductName(item.name);
                      setShowProductPicker(false);
                    }}
                  >
                    <ThemedText style={styles.productItemText}>{item.name}</ThemedText>
                    <ThemedText style={styles.productStock}>Stok: {item.stock}</ThemedText>
                  </TouchableOpacity>
                )}
                style={styles.productList}
              />

              <TouchableOpacity
                style={[styles.modalCloseButton, { backgroundColor: errorColor }]}
                onPress={() => setShowProductPicker(false)}
              >
                <ThemedText style={styles.modalCloseText}>İptal</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </TouchableOpacity>
        </Modal>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Android: Extra space for submit button visibility
  },
  container: {
    padding: 20,
    margin: 12,
    marginBottom: 0,
    borderRadius: 8,
    borderWidth: 1,
  },
  title: {
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.4,
  },
  formGroup: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginBottom: 12,
  },
  halfWidth: {
    flex: 1,
  },
  label: {
    marginBottom: 8,
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: -0.2,
  },
  input: {
    borderWidth: 1,
    borderRadius: 6,
    padding: 14,
    fontSize: 16,
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  pickerButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 48,
  },
  pickerButtonText: {
    fontSize: 16,
    flex: 1,
  },
  pickerArrow: {
    fontSize: 12,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'auto',
  },
  modalContent: {
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  productList: {
    maxHeight: 300,
  },
  productItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  productItemText: {
    fontSize: 16,
    fontWeight: '600',
  },
  productStock: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  modalCloseButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 15,
  },
  modalCloseText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    backgroundColor: 'rgba(0, 122, 255, 0.08)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  previewTitle: {
    textAlign: 'center',
    marginBottom: 10,
    color: '#007AFF',
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  previewLabel: {
    fontSize: 14,
  },
  previewValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  unitRow: {
    borderTopWidth: 1,
    borderTopColor: '#34c759',
    paddingTop: 8,
    marginTop: 8,
  },
  unitLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#34c759',
  },
  unitValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#34c759',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#007AFF',
    paddingTop: 10,
    marginTop: 10,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  submitButton: {
    padding: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    opacity: 0.5,
    shadowOpacity: 0,
    elevation: 0,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});
