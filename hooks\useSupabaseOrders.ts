import { useSupabaseAuthForced } from '@/hooks/useSupabaseAuthForced';
import { supabase } from '@/lib/supabase';
import { Order } from '@/types/Order';
import { useCallback, useEffect, useState } from 'react';

export const useSupabaseOrders = () => {
  const { user } = useSupabaseAuthForced();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Only for initial load
  const [isRefreshing, setIsRefreshing] = useState(false); // For background refresh
  const [error, setError] = useState<string | null>(null);

  // Fetch orders from Supabase with optional silent refresh
  const fetchOrders = useCallback(async (silent = false) => {
    try {
      if (!silent) {
        setIsLoading(true); // Only show loading for manual refresh
      } else {
        setIsRefreshing(true); // Silent background refresh
      }
      setError(null);

      // Check user authentication
      if (!user?.id) {
        console.log('ℹ️ [ORDERS] No authenticated user, returning empty list');
        setOrders([]);
        return;
      }

      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', user.id) // Filter by user
        .order('order_date', { ascending: false });

      if (error) {
        throw error;
      }

      const formattedOrders: Order[] = data.map(item => ({
        id: item.id,
        productId: item.product_id,
        productName: item.product_name,
        customerName: item.customer_name,
        quantity: item.quantity,
        orderDate: new Date(item.order_date),
        status: item.status as 'pending' | 'completed' | 'cancelled',
        salePrice: item.sale_price,
      }));

      // Sort orders: pending first, then completed, then cancelled
      const sortedOrders = formattedOrders.sort((a, b) => {
        const statusOrder = { pending: 0, completed: 1, cancelled: 2 };
        if (statusOrder[a.status] !== statusOrder[b.status]) {
          return statusOrder[a.status] - statusOrder[b.status];
        }
        return b.orderDate.getTime() - a.orderDate.getTime();
      });

      setOrders(sortedOrders);
    } catch (error: any) {
      console.error('Error fetching orders:', error);
      setError(error.message || 'Failed to fetch orders');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [user?.id]);

  // Add order
  const addOrder = useCallback(async (orderData: Omit<Order, 'id' | 'orderDate'>): Promise<boolean> => {
    try {
      setError(null);

      // Check user authentication
      if (!user?.id) {
        throw new Error('User must be authenticated to add orders');
      }

      // Ensure all required fields are provided with proper validation
      const insertData = {
        product_id: orderData.productId || null, // Can be null for products not in inventory
        product_name: orderData.productName?.trim() || '', // Ensure not empty
        customer_name: orderData.customerName?.trim() || '', // Ensure not empty
        quantity: orderData.quantity || 1, // Default to 1 if not provided
        status: orderData.status || 'pending', // Default to pending
        sale_price: orderData.salePrice || null, // Can be null initially
        order_date: new Date().toISOString(), // Explicitly set order_date
        user_id: user.id, // Required for RLS
      };

      // Validate required fields before insertion
      if (!insertData.product_name) {
        throw new Error('Ürün adı gereklidir');
      }
      if (!insertData.customer_name) {
        throw new Error('Müşteri adı gereklidir');
      }
      if (insertData.quantity <= 0) {
        throw new Error('Miktar 0\'dan büyük olmalıdır');
      }

      console.log('Inserting order with data:', insertData);

      const { data, error } = await supabase
        .from('orders')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      const newOrder: Order = {
        id: data.id,
        productId: data.product_id,
        productName: data.product_name,
        customerName: data.customer_name,
        quantity: data.quantity,
        orderDate: new Date(data.order_date),
        status: data.status,
        salePrice: data.sale_price,
      };

      setOrders(prev => {
        const updated = [newOrder, ...prev];
        return updated.sort((a, b) => {
          const statusOrder = { pending: 0, completed: 1, cancelled: 2 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return b.orderDate.getTime() - a.orderDate.getTime();
        });
      });
      return true;
    } catch (error: any) {
      console.error('Error adding order:', error);
      setError(error.message || 'Failed to add order');
      return false;
    }
  }, [user?.id]);

  // Update order status
  const updateOrderStatus = useCallback(async (id: string, status: 'pending' | 'completed' | 'cancelled', salePrice?: number): Promise<boolean> => {
    try {
      setError(null);

      // Check user authentication
      if (!user?.id) {
        throw new Error('User must be authenticated to update orders');
      }

      const updateData: any = { status };
      if (salePrice !== undefined) {
        updateData.sale_price = salePrice;
      }

      const { data, error } = await supabase
        .from('orders')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id) // Ensure user can only update their own orders
        .select()
        .single();

      if (error) {
        throw error;
      }

      // ENHANCED STOCK MANAGEMENT: Handle both completed and cancelled orders
      const order = orders.find(o => o.id === id);
      const previousStatus = order?.status;

      console.log('🔄 Order status change:', {
        orderId: id,
        previousStatus,
        newStatus: status,
        productId: order?.productId,
        productName: order?.productName,
        quantity: order?.quantity
      });

      // Update stock based on status changes
      if (order && order.productId) {
        await updateProductStock(order, previousStatus, status);
      } else if (order && !order.productId) {
        // For products not in inventory, try to find by name
        await updateProductStockByName(order, previousStatus, status);
      }

      setOrders(prev => {
        const updated = prev.map(order => 
          order.id === id 
            ? { ...order, status, salePrice: salePrice ?? order.salePrice }
            : order
        );
        return updated.sort((a, b) => {
          const statusOrder = { pending: 0, completed: 1, cancelled: 2 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return b.orderDate.getTime() - a.orderDate.getTime();
        });
      });
      return true;
    } catch (error: any) {
      console.error('Error updating order status:', error);
      setError(error.message || 'Failed to update order status');
      return false;
    }
  }, [orders, user?.id]);

  // STOCK MANAGEMENT FUNCTIONS
  const updateProductStock = async (order: any, previousStatus: string | undefined, newStatus: string) => {
    try {
      console.log('📦 Updating product stock by ID:', {
        productId: order.productId,
        productName: order.productName,
        quantity: order.quantity,
        previousStatus,
        newStatus
      });

      // Get current product stock (only user's own products)
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('stock')
        .eq('id', order.productId)
        .eq('user_id', user?.id) // Filter by user
        .single();

      if (productError) {
        console.error('❌ Error fetching product:', productError);
        return;
      }

      if (!productData) {
        console.warn('⚠️ Product not found with ID:', order.productId);
        return;
      }

      let stockChange = 0;

      // Calculate stock change based on status transition
      if (previousStatus !== 'completed' && newStatus === 'completed') {
        // Order completed: reduce stock
        stockChange = -order.quantity;
        console.log('📉 Order completed: reducing stock by', order.quantity);
      } else if (previousStatus === 'completed' && newStatus === 'cancelled') {
        // Order cancelled (was completed): restore stock
        stockChange = order.quantity;
        console.log('📈 Order cancelled (was completed): restoring stock by', order.quantity);
      } else if (previousStatus === 'completed' && newStatus === 'pending') {
        // Order reverted to pending (was completed): restore stock
        stockChange = order.quantity;
        console.log('📈 Order reverted to pending: restoring stock by', order.quantity);
      }

      if (stockChange !== 0) {
        const newStock = Math.max(0, productData.stock + stockChange);
        console.log(`📊 Stock update: ${productData.stock} → ${newStock} (change: ${stockChange})`);

        const { error: updateError } = await supabase
          .from('products')
          .update({ stock: newStock })
          .eq('id', order.productId)
          .eq('user_id', user?.id); // Filter by user

        if (updateError) {
          console.error('❌ Error updating product stock:', updateError);
        } else {
          console.log('✅ Product stock updated successfully');
        }
      } else {
        console.log('ℹ️ No stock change needed for this status transition');
      }
    } catch (error) {
      console.error('❌ Error in updateProductStock:', error);
    }
  };

  const updateProductStockByName = async (order: any, previousStatus: string | undefined, newStatus: string) => {
    try {
      console.log('📦 Updating product stock by name:', {
        productName: order.productName,
        quantity: order.quantity,
        previousStatus,
        newStatus
      });

      // Find product by name (case insensitive, only user's own products)
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('id, stock')
        .ilike('name', order.productName)
        .eq('user_id', user?.id) // Filter by user
        .single();

      if (productError || !productData) {
        console.log('ℹ️ Product not found in inventory by name:', order.productName);
        return;
      }

      // Update the order with the found product ID for future reference
      await supabase
        .from('orders')
        .update({ product_id: productData.id })
        .eq('id', order.id);

      // Use the main stock update function
      await updateProductStock({ ...order, productId: productData.id }, previousStatus, newStatus);
    } catch (error) {
      console.error('❌ Error in updateProductStockByName:', error);
    }
  };

  // Delete order
  const deleteOrder = useCallback(async (id: string): Promise<boolean> => {
    try {
      setError(null);

      // Check user authentication
      if (!user?.id) {
        throw new Error('User must be authenticated to delete orders');
      }

      const { error } = await supabase
        .from('orders')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id); // Ensure user can only delete their own orders

      if (error) {
        throw error;
      }

      setOrders(prev => prev.filter(order => order.id !== id));
      return true;
    } catch (error: any) {
      console.error('Error deleting order:', error);
      setError(error.message || 'Failed to delete order');
      return false;
    }
  }, [user?.id]);

  // Initialize with silent refresh interval
  useEffect(() => {
    // Initial load (with loading indicator)
    fetchOrders(false);

    // Set up silent background refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      fetchOrders(true); // Silent refresh - no loading indicator
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [fetchOrders]);

  return {
    orders,
    isLoading, // Only true during initial load or manual refresh
    isRefreshing, // True during background refresh
    error,
    addOrder,
    updateOrderStatus,
    deleteOrder,
    refreshOrders: () => fetchOrders(false), // Manual refresh with loading indicator
    silentRefresh: () => fetchOrders(true), // Silent refresh without loading indicator
  };
};
