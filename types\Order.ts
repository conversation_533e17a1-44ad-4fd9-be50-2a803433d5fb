export interface Order {
  id: string;
  productId?: string; // Optional - for products not in inventory
  productName: string;
  customerName: string;
  quantity: number;
  orderDate: Date;
  createdAt?: Date;    // Alias for compatibility
  status: 'pending' | 'completed' | 'cancelled';
  salePrice?: number; // Price when completed
}

export interface OrderFormData {
  productName: string;
  customerName: string;
  quantity: string;
}
