import { supabase } from '@/lib/supabase';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

type RealtimeChannelStatus = 'SUBSCRIBED' | 'CHANNEL_ERROR' | 'CLOSED' | 'TIMED_OUT' | 'CHANNEL_CLOSED';

interface RetryableSubscribeOptions {
  channel: string;
  event: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
  schema?: string;
  table?: string;
  callback: (payload: RealtimePostgresChangesPayload<any>) => void;
  maxRetries?: number;
  initialDelay?: number;
}

interface RetryableSubscription {
  unsubscribe: () => void;
  channel: RealtimeChannel;
}

export function retryableSubscribe({
  channel,
  event,
  schema = 'public',
  table,
  callback,
  maxRetries = 5,
  initialDelay = 500
}: RetryableSubscribeOptions): RetryableSubscription {
  let retryCount = 0;
  let retryTimeout: ReturnType<typeof setTimeout> | null = null;
  let currentChannel: RealtimeChannel | null = null;

  const subscribe = () => {
    try {
      currentChannel = supabase
        .channel(channel)
        .on(
          'postgres_changes' as any,
          {
            event,
            schema,
            ...(table && { table })
          },
          callback
        )
        .subscribe((status: RealtimeChannelStatus) => {
          if (status === 'SUBSCRIBED') {
            // Reset retry count on successful subscription
            retryCount = 0;
          } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            handleError();
          }
        });
    } catch (error) {
      handleError();
    }
  };

  const handleError = () => {
    if (retryCount < maxRetries) {
      const delay = initialDelay * Math.pow(2, retryCount);
      console.warn(
        `Subscription to channel "${channel}" failed. Retrying in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`
      );

      retryTimeout = setTimeout(() => {
        retryCount++;
        if (currentChannel) {
          currentChannel.unsubscribe();
        }
        subscribe();
      }, delay);
    } else {
      console.error(
        `Failed to subscribe to channel "${channel}" after ${maxRetries} attempts`
      );
    }
  };

  const unsubscribe = () => {
    if (retryTimeout) {
      clearTimeout(retryTimeout);
    }
    if (currentChannel) {
      currentChannel.unsubscribe();
    }
  };

  // Initial subscription
  subscribe();

  return {
    unsubscribe,
    channel: currentChannel!
  };
} 