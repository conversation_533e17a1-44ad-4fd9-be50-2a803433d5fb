import { View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  noBackground?: boolean; // Option to disable automatic background
};

export function ThemedView({ style, lightColor, darkColor, noBackground, ...otherProps }: ThemedViewProps) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');

  // Only apply backgroundColor if not disabled
  const backgroundStyle = noBackground ? {} : { backgroundColor };

  return <View style={[backgroundStyle, style]} {...otherProps} />;
}
