const { withAndroidManifest } = require('@expo/config-plugins');

/**
 * Expo plugin to configure Android for full immersive mode
 */
const withImmersiveMode = (config) => {
  return withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Find the main activity
    const mainActivity = androidManifest.manifest.application[0].activity.find(
      (activity) => activity.$['android:name'] === '.MainActivity'
    );

    if (mainActivity) {
      // Add immersive mode attributes
      mainActivity.$['android:theme'] = '@style/Theme.App.SplashScreen';
      
      // Add window flags for immersive mode
      if (!mainActivity['meta-data']) {
        mainActivity['meta-data'] = [];
      }
      
      // Add immersive mode meta-data
      mainActivity['meta-data'].push({
        $: {
          'android:name': 'expo.modules.navigationbar.immersive',
          'android:value': 'true'
        }
      });
      
      // Add edge-to-edge meta-data
      mainActivity['meta-data'].push({
        $: {
          'android:name': 'expo.modules.systemui.edge_to_edge',
          'android:value': 'true'
        }
      });
    }

    return config;
  });
};

module.exports = withImmersiveMode;
