export interface Cost {
  id: string;
  product_name: string;
  productName?: string;       // Alias for compatibility
  quantity: number;          // Kaç adet için hesaplandı
  essence_price: number;      // Esans fiyatı (₺)
  essencePrice?: number;      // Alias for compatibility
  essence_amount: number;     // Esans şi<PERSON>e miktarı (ml)
  essenceAmount?: number;     // Alias for compatibility
  essence_used: number;       // Kullanılan esans miktarı (ml)
  essenceUsed?: number;       // Alias for compatibility
  alcohol_price: number;      // Alkol fiyatı (₺)
  alcoholPrice?: number;      // Alias for compatibility
  alcohol_used: number;       // Kullanılan alkol miktarı (ml)
  alcoholUsed?: number;       // Alias for compatibility
  bottle_price: number;       // Şişe fiyatı (₺)
  bottlePrice?: number;       // Alias for compatibility
  essence_cost: number;
  alcohol_cost: number;
  unit_cost: number;          // Birim maliyet (1 adet için)
  unitCost?: number;          // Alias for compatibility
  total_cost: number;         // Toplam maliyet (quantity * unitCost)
  totalCost?: number;         // Alias for compatibility
  created_at: string;
  createdAt?: Date;           // Alias for compatibility
  isOptimistic?: boolean;
}

export interface CostFormData {
  productName: string;
  quantity: string;
  essencePrice: string;
  essenceAmount: string;
  essenceUsed: string;
  alcoholPrice: string;
  alcoholUsed: string;
  bottlePrice: string;
}

export interface CostSummary {
  totalProducts: number;
  totalQuantity: number;      // Alias for compatibility
  totalCost: number;
  averageCost: number;
  averageUnitCost: number;    // Alias for compatibility
  totalEssenceUsed: number;
  totalAlcoholUsed: number;
}
