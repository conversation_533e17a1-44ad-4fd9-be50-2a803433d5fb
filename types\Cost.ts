export interface Cost {
  id: string;
  product_name: string;
  quantity: number;          // Kaç adet için hesaplandı
  essence_price: number;      // Esans fiyatı (₺)
  essence_amount: number;     // Esans şişe miktarı (ml)
  essence_used: number;       // Kullanılan esans miktarı (ml)
  alcohol_price: number;      // Alkol fiyatı (₺)
  alcohol_used: number;       // Kullanılan alkol miktarı (ml)
  bottle_price: number;       // Şişe fiyatı (₺)
  essence_cost: number;
  alcohol_cost: number;
  unit_cost: number;          // Birim maliyet (1 adet için)
  total_cost: number;         // Toplam maliyet (quantity * unitCost)
  created_at: string;
  isOptimistic?: boolean;
}

export interface CostFormData {
  productName: string;
  quantity: string;
  essencePrice: string;
  essenceAmount: string;
  essenceUsed: string;
  alcoholPrice: string;
  alcoholUsed: string;
  bottlePrice: string;
}

export interface CostSummary {
  totalProducts: number;
  totalCost: number;
  averageCost: number;
  totalEssenceUsed: number;
  totalAlcoholUsed: number;
}
