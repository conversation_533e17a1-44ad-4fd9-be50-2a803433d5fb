import { ProductForm } from '@/components/ProductForm';
import { ProductList } from '@/components/ProductList';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useSupabaseProducts as useProducts } from '@/hooks/useSupabaseProducts';
import { useThemeColor } from '@/hooks/useThemeColor';
import { ActivityIndicator, Platform, SafeAreaView, ScrollView, StatusBar, StyleSheet } from 'react-native';

export default function ProductsScreen() {
  const { products, isLoading: loading, isRefreshing, addProduct, deleteProduct, refreshProducts } = useProducts();
  const backgroundColor = useThemeColor({}, 'background');
  const absoluteBackgroundColor = useThemeColor({ light: '#F5F6FA', dark: '#000000' }, 'background');

  // Handle product addition with proper data structure
  const handleAddProduct = async (name: string, stock: number): Promise<void> => {
    try {
      console.log('🔄 [PRODUCTS] handleAddProduct çağrıldı:', {
        name,
        stock,
        nameType: typeof name,
        stockType: typeof stock
      });

      // ZORUNLU ALAN KONTROLÜ
      if (!name || typeof name !== 'string' || name.trim() === '') {
        throw new Error('Ürün adı zorunludur ve boş olamaz');
      }

      if (typeof stock !== 'number' || isNaN(stock) || stock < 0) {
        throw new Error('Stok miktarı sayı olmalı ve 0 veya daha büyük olmalıdır');
      }

      // Supabase için doğru veri yapısını hazırla
      const productData = {
        name: name.trim(), // NOT NULL - zorunlu
        stock: Number(stock), // NOT NULL - zorunlu, sayı olarak gönder
        description: '', // Opsiyonel - boş string
      };

      console.log('🔄 [PRODUCTS] addProduct\'a gönderilecek veri:', productData);

      const success = await addProduct(productData);
      if (!success) {
        throw new Error('Ürün ekleme işlemi başarısız oldu');
      }

      console.log('✅ [PRODUCTS] Ürün başarıyla eklendi');
    } catch (error: any) {
      console.error('❌ [PRODUCTS] handleAddProduct hatası:', error);
      throw error; // ProductForm'un hata mesajını göstermesi için yeniden fırlat
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.loadingText}>Ürünler yükleniyor...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
      <ThemedView style={styles.container} noBackground>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <ThemedView style={styles.header}>
            <ThemedText type="title" style={styles.title}>
              Ürün Yönetimi
            </ThemedText>

            {/* SMALL REFRESH INDICATOR FOR BACKGROUND UPDATES */}
            {isRefreshing && (
              <ThemedView style={styles.refreshIndicator}>
                <ActivityIndicator size="small" color="#007AFF" />
              </ThemedView>
            )}
          </ThemedView>

          <ProductForm onAddProduct={handleAddProduct} />

          <ProductList
            products={products}
            onDeleteProduct={deleteProduct}
            onRefresh={refreshProducts}
            scrollEnabled={false}
          />
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    // backgroundColor will be set dynamically via useThemeColor
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 0, // Remove bottom padding for Android
        marginBottom: -20, // Prevent bottom overlay
      },
    }),
  },

  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80, // Android: Space for tab bar
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    textAlign: 'center',
  },
  refreshIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
  },

});
