/**
 * New Products Hook with Backend Integration
 * Handles product CRUD operations with backend synchronization
 */

import { Product } from '@/types/Product';
import { productsApi, CreateProductRequest, UpdateStockRequest } from '@/services/productsApi';
import { ApiError } from '@/services/api';
import { useCallback, useEffect, useState } from 'react';

// Global products state for real-time sync
let globalProducts: Product[] = [];
let globalProductsListeners: Set<(products: Product[]) => void> = new Set();

// Global products state management
const setGlobalProducts = (products: Product[]) => {
  globalProducts = products;
  globalProductsListeners.forEach(listener => {
    try {
      listener(products);
    } catch (error) {
      if (__DEV__) console.error('Products listener error:', error);
    }
  });
};

const addGlobalProductsListener = (listener: (products: Product[]) => void) => {
  globalProductsListeners.add(listener);
  return () => globalProductsListeners.delete(listener);
};

/**
 * Enhanced Products Hook with Backend Integration
 */
export function useProductsNew() {
  const [products, setProducts] = useState<Product[]>(globalProducts);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load products from backend
   */
  const loadProducts = useCallback(async () => {
    try {
      if (__DEV__) console.log('📦 Loading products from backend...');
      setLoading(true);
      setError(null);

      const fetchedProducts = await productsApi.getProducts();
      
      setGlobalProducts(fetchedProducts);
      setProducts(fetchedProducts);

      if (__DEV__) console.log(`✅ Loaded ${fetchedProducts.length} products from backend`);
    } catch (error) {
      console.error('❌ Error loading products:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to load products';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Add new product
   */
  const addProduct = useCallback(async (name: string, stock: number, description?: string): Promise<void> => {
    try {
      if (__DEV__) console.log('📦 Adding product:', name);
      setError(null);

      const productData: CreateProductRequest = {
        name: name.trim(),
        stock,
        description: description?.trim(),
      };

      const newProduct = await productsApi.createProduct(productData);

      // Update global state
      const updatedProducts = [...globalProducts, newProduct];
      setGlobalProducts(updatedProducts);
      setProducts(updatedProducts);

      if (__DEV__) console.log('✅ Product added successfully:', newProduct.name);
    } catch (error) {
      console.error('❌ Error adding product:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to add product';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Update product stock
   */
  const updateProductStock = useCallback(async (productId: string, newStock: number): Promise<void> => {
    try {
      if (__DEV__) console.log('📦 Updating product stock:', productId, newStock);
      setError(null);

      const stockData: UpdateStockRequest = {
        id: productId,
        stock: newStock,
        operation: 'set',
      };

      const updatedProduct = await productsApi.updateProductStock(stockData);

      // Update global state
      const updatedProducts = globalProducts.map(product =>
        product.id === productId ? updatedProduct : product
      );
      setGlobalProducts(updatedProducts);
      setProducts(updatedProducts);

      if (__DEV__) console.log('✅ Product stock updated successfully');
    } catch (error) {
      console.error('❌ Error updating product stock:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to update product stock';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Reduce product stock (for orders)
   */
  const reduceProductStock = useCallback(async (productId: string, quantity: number): Promise<void> => {
    try {
      if (__DEV__) console.log('📦 Reducing product stock:', productId, quantity);
      setError(null);

      const stockData: UpdateStockRequest = {
        id: productId,
        stock: quantity,
        operation: 'subtract',
      };

      const updatedProduct = await productsApi.updateProductStock(stockData);

      // Update global state
      const updatedProducts = globalProducts.map(product =>
        product.id === productId ? updatedProduct : product
      );
      setGlobalProducts(updatedProducts);
      setProducts(updatedProducts);

      if (__DEV__) console.log('✅ Product stock reduced successfully');
    } catch (error) {
      console.error('❌ Error reducing product stock:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to reduce product stock';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Delete product
   */
  const deleteProduct = useCallback(async (productId: string): Promise<void> => {
    try {
      if (__DEV__) console.log('📦 Deleting product:', productId);
      setError(null);

      await productsApi.deleteProduct(productId);

      // Update global state
      const updatedProducts = globalProducts.filter(product => product.id !== productId);
      setGlobalProducts(updatedProducts);
      setProducts(updatedProducts);

      if (__DEV__) console.log('✅ Product deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting product:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to delete product';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Search products
   */
  const searchProducts = useCallback(async (query: string): Promise<Product[]> => {
    try {
      if (__DEV__) console.log('📦 Searching products:', query);
      setError(null);

      const searchResults = await productsApi.searchProducts(query);

      if (__DEV__) console.log(`✅ Found ${searchResults.length} products`);
      return searchResults;
    } catch (error) {
      console.error('❌ Error searching products:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to search products';
      setError(errorMessage);
      return [];
    }
  }, []);

  /**
   * Refresh products from backend
   */
  const refreshProducts = useCallback(async (): Promise<void> => {
    await loadProducts();
  }, [loadProducts]);

  /**
   * Get product by ID
   */
  const getProductById = useCallback((productId: string): Product | undefined => {
    return products.find(product => product.id === productId);
  }, [products]);

  /**
   * Get product by name
   */
  const getProductByName = useCallback((productName: string): Product | undefined => {
    return products.find(product => 
      product.name.toLowerCase() === productName.toLowerCase()
    );
  }, [products]);

  // Global products listener for real-time sync
  useEffect(() => {
    const unsubscribe = addGlobalProductsListener((newProducts) => {
      if (__DEV__) console.log('🔄 useProducts received global products update');
      setProducts(newProducts);
    });

    return unsubscribe;
  }, []);

  // Load products on mount
  useEffect(() => {
    loadProducts();

    // Set up periodic refresh for real-time sync (every 30 seconds)
    const interval = setInterval(() => {
      if (__DEV__) console.log('🔄 Periodic products refresh');
      loadProducts();
    }, 30000);

    return () => clearInterval(interval);
  }, [loadProducts]);

  // Debug products state changes (only in development)
  useEffect(() => {
    if (__DEV__) console.log('🔄 useProducts state changed:', { 
      count: products.length, 
      loading, 
      error 
    });
  }, [products, loading, error]);

  return {
    products,
    loading,
    error,
    addProduct,
    updateProductStock,
    reduceProductStock,
    deleteProduct,
    searchProducts,
    refreshProducts,
    getProductById,
    getProductByName,
  };
}
