/**
 * Orders API Service
 * Handles order CRUD operations with backend synchronization
 */

import { Order } from '@/types/Order';
import { apiClient, ApiError } from './api';

export interface OrdersResponse {
  success: boolean;
  orders: Order[];
  total?: number;
  page?: number;
  limit?: number;
}

export interface OrderResponse {
  success: boolean;
  order: Order;
  message?: string;
}

export interface DeleteOrderResponse {
  success: boolean;
  message: string;
}

export interface CreateOrderRequest {
  productId?: string;
  productName: string;
  customerName: string;
  quantity: number;
  salePrice?: number;
}

export interface UpdateOrderRequest {
  id: string;
  productId?: string;
  productName?: string;
  customerName?: string;
  quantity?: number;
  status?: Order['status'];
  salePrice?: number;
}

export interface UpdateOrderStatusRequest {
  id: string;
  status: Order['status'];
  salePrice?: number;
}

// Orders API endpoints
const ORDERS_ENDPOINTS = {
  LIST: '/orders',
  CREATE: '/orders',
  GET: (id: string) => `/orders/${id}`,
  UPDATE: (id: string) => `/orders/${id}`,
  DELETE: (id: string) => `/orders/${id}`,
  UPDATE_STATUS: (id: string) => `/orders/${id}/status`,
  SEARCH: '/orders/search',
  BY_STATUS: (status: string) => `/orders/status/${status}`,
} as const;

/**
 * Orders API Service Class
 */
export class OrdersApiService {
  /**
   * Get all orders
   */
  async getOrders(page = 1, limit = 100): Promise<Order[]> {
    try {
      console.log('📋 API: Fetching orders');

      const response = await apiClient.get<OrdersResponse>(
        `${ORDERS_ENDPOINTS.LIST}?page=${page}&limit=${limit}`
      );

      if (response.success && response.orders) {
        console.log(`✅ API: Fetched ${response.orders.length} orders`);
        
        // Convert date strings to Date objects
        const orders = response.orders.map(order => ({
          ...order,
          orderDate: new Date(order.orderDate),
        }));
        
        return orders;
      } else {
        throw new ApiError('Failed to fetch orders');
      }
    } catch (error) {
      console.error('❌ API: Orders fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch orders');
    }
  }

  /**
   * Get single order by ID
   */
  async getOrder(id: string): Promise<Order> {
    try {
      console.log('📋 API: Fetching order:', id);

      const response = await apiClient.get<OrderResponse>(ORDERS_ENDPOINTS.GET(id));

      if (response.success && response.order) {
        console.log('✅ API: Order fetched successfully');
        
        // Convert date string to Date object
        const order = {
          ...response.order,
          orderDate: new Date(response.order.orderDate),
        };
        
        return order;
      } else {
        throw new ApiError('Order not found');
      }
    } catch (error) {
      console.error('❌ API: Order fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch order');
    }
  }

  /**
   * Create new order
   */
  async createOrder(orderData: CreateOrderRequest): Promise<Order> {
    try {
      console.log('📋 API: Creating order for:', orderData.customerName);

      const response = await apiClient.post<OrderResponse>(ORDERS_ENDPOINTS.CREATE, {
        productId: orderData.productId,
        productName: orderData.productName.trim(),
        customerName: orderData.customerName.trim(),
        quantity: orderData.quantity,
        salePrice: orderData.salePrice,
      });

      if (response.success && response.order) {
        console.log('✅ API: Order created successfully');
        
        // Convert date string to Date object
        const order = {
          ...response.order,
          orderDate: new Date(response.order.orderDate),
        };
        
        return order;
      } else {
        throw new ApiError(response.message || 'Failed to create order');
      }
    } catch (error) {
      console.error('❌ API: Order creation error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to create order');
    }
  }

  /**
   * Update existing order
   */
  async updateOrder(orderData: UpdateOrderRequest): Promise<Order> {
    try {
      console.log('📋 API: Updating order:', orderData.id);

      const updateData: any = {};
      if (orderData.productId !== undefined) updateData.productId = orderData.productId;
      if (orderData.productName !== undefined) updateData.productName = orderData.productName.trim();
      if (orderData.customerName !== undefined) updateData.customerName = orderData.customerName.trim();
      if (orderData.quantity !== undefined) updateData.quantity = orderData.quantity;
      if (orderData.status !== undefined) updateData.status = orderData.status;
      if (orderData.salePrice !== undefined) updateData.salePrice = orderData.salePrice;

      const response = await apiClient.put<OrderResponse>(
        ORDERS_ENDPOINTS.UPDATE(orderData.id),
        updateData
      );

      if (response.success && response.order) {
        console.log('✅ API: Order updated successfully');
        
        // Convert date string to Date object
        const order = {
          ...response.order,
          orderDate: new Date(response.order.orderDate),
        };
        
        return order;
      } else {
        throw new ApiError(response.message || 'Failed to update order');
      }
    } catch (error) {
      console.error('❌ API: Order update error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to update order');
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(statusData: UpdateOrderStatusRequest): Promise<Order> {
    try {
      console.log('📋 API: Updating order status:', statusData.id, statusData.status);

      const response = await apiClient.put<OrderResponse>(
        ORDERS_ENDPOINTS.UPDATE_STATUS(statusData.id),
        {
          status: statusData.status,
          salePrice: statusData.salePrice,
        }
      );

      if (response.success && response.order) {
        console.log('✅ API: Order status updated successfully');
        
        // Convert date string to Date object
        const order = {
          ...response.order,
          orderDate: new Date(response.order.orderDate),
        };
        
        return order;
      } else {
        throw new ApiError(response.message || 'Failed to update order status');
      }
    } catch (error) {
      console.error('❌ API: Order status update error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to update order status');
    }
  }

  /**
   * Delete order
   */
  async deleteOrder(id: string): Promise<void> {
    try {
      console.log('📋 API: Deleting order:', id);

      const response = await apiClient.delete<DeleteOrderResponse>(ORDERS_ENDPOINTS.DELETE(id));

      if (response.success) {
        console.log('✅ API: Order deleted successfully');
      } else {
        throw new ApiError(response.message || 'Failed to delete order');
      }
    } catch (error) {
      console.error('❌ API: Order deletion error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to delete order');
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(status: Order['status']): Promise<Order[]> {
    try {
      console.log('📋 API: Fetching orders by status:', status);

      const response = await apiClient.get<OrdersResponse>(ORDERS_ENDPOINTS.BY_STATUS(status));

      if (response.success && response.orders) {
        console.log(`✅ API: Found ${response.orders.length} orders with status ${status}`);
        
        // Convert date strings to Date objects
        const orders = response.orders.map(order => ({
          ...order,
          orderDate: new Date(order.orderDate),
        }));
        
        return orders;
      } else {
        return [];
      }
    } catch (error) {
      console.error('❌ API: Orders by status fetch error:', error);
      return [];
    }
  }

  /**
   * Search orders by customer name or product name
   */
  async searchOrders(query: string): Promise<Order[]> {
    try {
      console.log('📋 API: Searching orders:', query);

      const response = await apiClient.get<OrdersResponse>(
        `${ORDERS_ENDPOINTS.SEARCH}?q=${encodeURIComponent(query)}`
      );

      if (response.success && response.orders) {
        console.log(`✅ API: Found ${response.orders.length} orders`);
        
        // Convert date strings to Date objects
        const orders = response.orders.map(order => ({
          ...order,
          orderDate: new Date(order.orderDate),
        }));
        
        return orders;
      } else {
        return [];
      }
    } catch (error) {
      console.error('❌ API: Order search error:', error);
      return [];
    }
  }
}

// Create and export orders API service instance
export const ordersApi = new OrdersApiService();

// Types are already exported above
