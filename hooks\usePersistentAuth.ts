import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect } from 'react';
import { useSupabaseAuthForced } from './useSupabaseAuthForced';

const SESSION_KEY = '@auth_session';

export const usePersistentAuth = () => {
  const auth = useSupabaseAuthForced();

  // Save session to AsyncStorage when auth state changes
  useEffect(() => {
    const saveSession = async () => {
      if (auth.isAuthenticated && auth.user) {
        try {
          const { data: { session } } = await supabase.auth.getSession();
          if (session) {
            await AsyncStorage.setItem(SESSION_KEY, JSON.stringify({
              access_token: session.access_token,
              refresh_token: session.refresh_token,
              expires_at: session.expires_at,
            }));
          }
        } catch (error) {
          console.error('Error saving session:', error);
        }
      } else {
        // Clear session when logged out
        try {
          await AsyncStorage.removeItem(SESSION_KEY);
        } catch (error) {
          console.error('Error clearing session:', error);
        }
      }
    };

    saveSession();
  }, [auth.isAuthenticated, auth.user]);

  // Restore session on app start
  useEffect(() => {
    const restoreSession = async () => {
      try {
        const savedSession = await AsyncStorage.getItem(SESSION_KEY);
        if (savedSession) {
          const { access_token, refresh_token, expires_at } = JSON.parse(savedSession);
          
          // Check if session is still valid
          if (expires_at && new Date(expires_at) > new Date()) {
            const { data: { session }, error } = await supabase.auth.setSession({
              access_token,
              refresh_token,
            });

            if (error) {
              console.error('Error restoring session:', error);
              await AsyncStorage.removeItem(SESSION_KEY);
            } else if (session) {
              console.log('Session restored successfully');
            }
          } else {
            // Session expired, try to refresh
            const { data: { session }, error } = await supabase.auth.refreshSession();
            if (error) {
              console.error('Error refreshing session:', error);
              await AsyncStorage.removeItem(SESSION_KEY);
            }
          }
        }
      } catch (error) {
        console.error('Error restoring session:', error);
      }
    };

    restoreSession();
  }, []);

  return auth;
}; 