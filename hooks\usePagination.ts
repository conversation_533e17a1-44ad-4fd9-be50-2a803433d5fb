import { useCallback, useRef, useState } from 'react';

interface UsePaginationOptions {
  pageSize?: number;
  initialPage?: number;
}

export function usePagination(options: UsePaginationOptions = {}) {
  const { pageSize = 20, initialPage = 0 } = options;
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [hasMore, setHasMore] = useState(true);
  const [isFetchingMore, setFetchingMore] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const resetPagination = useCallback(() => {
    setCurrentPage(initialPage);
    setHasMore(true);
    setFetchingMore(false);
  }, [initialPage]);

  const incrementPage = useCallback(() => {
    setCurrentPage(prev => prev + 1);
  }, []);

  const getRange = useCallback(() => {
    const start = currentPage * pageSize;
    const end = start + pageSize - 1;
    return { start, end };
  }, [currentPage, pageSize]);

  const createAbortController = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();
    return abortControllerRef.current;
  }, []);

  return {
    currentPage,
    hasMore,
    isFetchingMore,
    setHasMore,
    setFetchingMore,
    incrementPage,
    getRange,
    resetPagination,
    createAbortController
  };
} 