import { RippleButton } from '@/components/RippleButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Product } from '@/types/Product';
import React from 'react';
import { Alert, StyleSheet } from 'react-native';

interface ProductItemProps {
  product: Product;
  onDelete: (productId: string) => Promise<void>;
}

export function ProductItem({ product, onDelete }: ProductItemProps) {
  const borderColor = useThemeColor({}, 'border');
  const iconColor = useThemeColor({ light: '#FF3B30', dark: '#FF453A' }, 'error');

  // Ensure product and its properties exist
  if (!product) {
    return null;
  }

  // Stock level calculation with safe defaults
  const maxStock = 100;
  const stock = typeof product.stock === 'number' ? product.stock : 0;
  const stockPercentage = Math.min((stock / maxStock) * 100, 100);

  // Stock level color based on percentage
  const getStockColor = () => {
    if (stockPercentage > 60) return '#34C759'; // Green
    if (stockPercentage > 30) return '#FF9500'; // Orange
    return '#FF3B30'; // Red
  };

  const handleDelete = () => {
    if (!product.id) {
      console.error('Cannot delete product: No ID provided');
      return;
    }

    Alert.alert(
      'Ürünü Sil',
      `"${product.name || 'İsimsiz Ürün'}" ürününü silmek istediğinizden emin misiniz?`,
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => onDelete(product.id),
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Tarih belirtilmemiş';
      }
      return date.toLocaleDateString('tr-TR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Tarih belirtilmemiş';
    }
  };

  return (
    <ThemedView style={[styles.container, { borderColor }]}>
      <ThemedView style={styles.content}>
        <ThemedView style={styles.info}>
          <ThemedText type="defaultSemiBold" style={styles.name}>
            {product.name || 'İsimsiz Ürün'}
          </ThemedText>
          <ThemedText style={styles.stock}>
            Stok: {stock} adet
          </ThemedText>

          {/* Stock Progress Bar */}
          <ThemedView style={styles.progressContainer}>
            <ThemedView style={styles.progressBackground}>
              <ThemedView
                style={[
                  styles.progressFill,
                  {
                    width: `${stockPercentage}%`,
                    backgroundColor: getStockColor()
                  }
                ]}
              />
            </ThemedView>
            <ThemedText style={[styles.progressText, { color: getStockColor() }]}>
              {stockPercentage.toFixed(0)}%
            </ThemedText>
          </ThemedView>
          <ThemedText style={styles.date}>
            Eklenme: {formatDate(product.created_at)}
          </ThemedText>
        </ThemedView>
        
        <RippleButton
          title=""
          onPress={handleDelete}
          style={styles.deleteButton}
          rippleColor="rgba(255, 59, 48, 0.3)"
        >
          <IconSymbol
            name="trash"
            size={24}
            color={iconColor}
          />
        </RippleButton>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  stock: {
    fontSize: 15,
    marginBottom: 4,
    opacity: 0.7,
    letterSpacing: -0.2,
  },
  date: {
    fontSize: 13,
    opacity: 0.6,
    letterSpacing: -0.1,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 4,
  },
  progressBackground: {
    flex: 1,
    height: 6,
    backgroundColor: '#E5E5EA',
    borderRadius: 3,
    overflow: 'hidden',
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
    minWidth: 2,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
});
