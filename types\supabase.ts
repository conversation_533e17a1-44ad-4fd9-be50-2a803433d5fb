export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      costs: {
        Row: {
          id: string
          product_name: string
          quantity: number
          essence_price: number
          essence_amount: number
          essence_used: number
          alcohol_price: number
          alcohol_used: number
          bottle_price: number
          essence_cost: number
          alcohol_cost: number
          unit_cost: number
          total_cost: number
          created_at: string
        }
        Insert: {
          id?: string
          product_name: string
          quantity: number
          essence_price: number
          essence_amount: number
          essence_used: number
          alcohol_price: number
          alcohol_used: number
          bottle_price: number
          essence_cost: number
          alcohol_cost: number
          unit_cost: number
          total_cost: number
          created_at?: string
        }
        Update: {
          id?: string
          product_name?: string
          quantity?: number
          essence_price?: number
          essence_amount?: number
          essence_used?: number
          alcohol_price?: number
          alcohol_used?: number
          bottle_price?: number
          essence_cost?: number
          alcohol_cost?: number
          unit_cost?: number
          total_cost?: number
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          stock: number
          description: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          stock: number
          description: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          stock?: number
          description?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
} 