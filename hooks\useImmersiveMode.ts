import * as NavigationBar from 'expo-navigation-bar';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef } from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';

interface ImmersiveModeOptions {
  hideNavigationBar?: boolean;
  hideStatusBar?: boolean;
  autoHideOnFocus?: boolean;
  enableSwipeGesture?: boolean;
}

export const useImmersiveMode = (options: ImmersiveModeOptions = {}) => {
  const {
    hideNavigationBar = true,
    hideStatusBar = true,
    autoHideOnFocus = true,
    enableSwipeGesture = true,
  } = options;

  const appState = useRef(AppState.currentState);

  // Ana immersive mode fonksiyonu
  const enableImmersiveMode = async () => {
    if (Platform.OS !== 'android') return;

    try {
      console.log('🎯 [IMMERSIVE] Enabling full immersive mode...');

      if (hideNavigationBar) {
        // Navigation bar'ı gizle
        await NavigationBar.setVisibilityAsync('hidden');
        
        // Navigation bar rengini transparan yap
        await NavigationBar.setBackgroundColorAsync('#00000000');
        
        console.log('✅ [IMMERSIVE] Navigation bar hidden');
      }

      console.log('🎯 [IMMERSIVE] Full immersive mode enabled successfully');
    } catch (error) {
      console.error('❌ [IMMERSIVE] Failed to enable immersive mode:', error);
    }
  };

  // App state değişikliklerini dinle
  useEffect(() => {
    if (!autoHideOnFocus || Platform.OS !== 'android') return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('🔄 [IMMERSIVE] App state changed:', appState.current, '->', nextAppState);

      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // Uygulama ön plana geldiğinde immersive mode'u yeniden aktifleştir
        console.log('🎯 [IMMERSIVE] App became active, re-enabling immersive mode...');
        setTimeout(enableImmersiveMode, 100); // Kısa gecikme ile
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [autoHideOnFocus]);

  // Component mount olduğunda immersive mode'u aktifleştir
  useEffect(() => {
    enableImmersiveMode();
  }, []);

  // Sistem çubuklarını geri getirme fonksiyonu (kullanıcı etkileşimi için)
  const showSystemBars = async () => {
    if (Platform.OS !== 'android') return;

    try {
      await NavigationBar.setVisibilityAsync('visible');
      console.log('👆 [IMMERSIVE] System bars shown temporarily');
      
      // 3 saniye sonra tekrar gizle
      setTimeout(enableImmersiveMode, 3000);
    } catch (error) {
      console.error('❌ [IMMERSIVE] Failed to show system bars:', error);
    }
  };

  return {
    enableImmersiveMode,
    showSystemBars,
    isAndroid: Platform.OS === 'android',
  };
};

// Immersive mode için özel StatusBar component'i
export const ImmersiveStatusBar = ({ 
  theme = 'dark' as 'light' | 'dark',
  useSystemTheme = false 
}: { 
  theme?: 'light' | 'dark';
  useSystemTheme?: boolean;
}) => {
  if (Platform.OS !== 'android') {
    return React.createElement(StatusBar, { style: useSystemTheme ? "auto" : theme });
  }

  return React.createElement(StatusBar, {
    style: useSystemTheme ? "auto" : theme,
    hidden: true,
    translucent: true,
    backgroundColor: "transparent"
  });
};
