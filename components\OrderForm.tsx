import { AnimatedButton } from '@/components/AnimatedButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Product } from '@/types/Product';
import { useState } from 'react';
import { Alert, FlatList, Modal, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

interface OrderFormProps {
  products: Product[];
  onAddOrder: (productName: string, customerName: string, quantity: number) => Promise<void>;
}

export function OrderForm({ onAddOrder, products }: OrderFormProps) {
  const [productName, setProductName] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showProductPicker, setShowProductPicker] = useState(false);

  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({ light: '#ddd', dark: '#444' }, 'text');

  // Modal theme colors
  const modalBackgroundColor = useThemeColor({}, 'background');
  const modalTitleColor = useThemeColor({ light: '#007AFF', dark: '#0A84FF' }, 'text');
  const productItemBackgroundColor = useThemeColor({ light: 'rgba(0, 122, 255, 0.05)', dark: 'rgba(10, 132, 255, 0.15)' }, 'background');
  const productItemBorderColor = useThemeColor({ light: 'rgba(0, 122, 255, 0.2)', dark: 'rgba(10, 132, 255, 0.3)' }, 'text');
  const productTextColor = useThemeColor({}, 'text');
  const stockTextColor = useThemeColor({ light: '#34C759', dark: '#30D158' }, 'text');

  const handleSubmit = async () => {
    if (!productName.trim()) {
      Alert.alert('Hata', 'Lütfen ürün adını girin');
      return;
    }

    if (!customerName.trim()) {
      Alert.alert('Hata', 'Lütfen müşteri adını girin');
      return;
    }

    if (quantity <= 0) {
      Alert.alert('Hata', 'Miktar 0\'dan büyük olmalıdır');
      return;
    }

    setIsSubmitting(true);
    try {
      await onAddOrder(productName.trim(), customerName.trim(), quantity);
      setProductName('');
      setCustomerName('');
      setQuantity(1);
      Alert.alert('Başarılı', 'Sipariş başarıyla oluşturuldu');
    } catch (error) {
      Alert.alert('Hata', 'Sipariş oluşturulurken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        Yeni Sipariş Oluştur
      </ThemedText>

      <ThemedView style={styles.formGroup}>
        <ThemedText style={styles.label}>Ürün Adı</ThemedText>
        <TouchableOpacity
          style={[styles.pickerButton, { borderColor, backgroundColor }]}
          onPress={() => setShowProductPicker(true)}
          disabled={isSubmitting}
        >
          <ThemedText style={[styles.pickerButtonText, { color: productName ? textColor : borderColor }]}>
            {productName || 'Ürün seçiniz...'}
          </ThemedText>
          <ThemedText style={[styles.pickerArrow, { color: textColor }]}>▼</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ThemedView style={styles.formGroup}>
        <ThemedText style={styles.label}>Müşteri Adı</ThemedText>
        <TextInput
          style={[
            styles.input,
            {
              color: textColor,
              backgroundColor: backgroundColor,
              borderColor: borderColor
            }
          ]}
          value={customerName}
          onChangeText={setCustomerName}
          placeholder="Müşteri adını giriniz"
          placeholderTextColor={borderColor}
          editable={!isSubmitting}
        />
      </ThemedView>

      <ThemedView style={styles.formGroup}>
        <ThemedText style={styles.label}>Miktar</ThemedText>
        <ThemedView style={styles.quantityContainer}>
          <TouchableOpacity
            style={[styles.quantityButton, { borderColor }]}
            onPress={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={isSubmitting || quantity <= 1}
          >
            <ThemedText style={styles.quantityButtonText}>-</ThemedText>
          </TouchableOpacity>
          
          <ThemedText style={styles.quantityText}>{quantity}</ThemedText>
          
          <TouchableOpacity
            style={[styles.quantityButton, { borderColor }]}
            onPress={() => setQuantity(quantity + 1)}
            disabled={isSubmitting}
          >
            <ThemedText style={styles.quantityButtonText}>+</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      <AnimatedButton
        title={isSubmitting ? 'Oluşturuluyor...' : 'Sipariş Oluştur'}
        onPress={handleSubmit}
        disabled={isSubmitting}
        variant="primary"
        size="large"
        style={StyleSheet.flatten([styles.submitButton, { backgroundColor: '#FF9500' }])}
      />

      {/* Product Picker Modal */}
      <Modal
        visible={showProductPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowProductPicker(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowProductPicker(false)}
        >
          <ThemedView style={[styles.modalContent, { backgroundColor: modalBackgroundColor }]}>
            <ThemedText style={[styles.modalTitle, { color: modalTitleColor }]}>Ürün Seçin</ThemedText>

            <FlatList
              data={products}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.productItem,
                    {
                      backgroundColor: productItemBackgroundColor,
                      borderColor: productItemBorderColor
                    }
                  ]}
                  onPress={() => {
                    setProductName(item.name);
                    setShowProductPicker(false);
                  }}
                >
                  <ThemedText style={[styles.productItemText, { color: productTextColor }]}>{item.name}</ThemedText>
                  <ThemedText style={[styles.productStock, { color: stockTextColor }]}>Stok: {item.stock}</ThemedText>
                </TouchableOpacity>
              )}
              style={styles.productList}
            />

            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowProductPicker(false)}
            >
              <ThemedText style={[styles.modalCloseText, { color: '#fff' }]}>İptal</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </TouchableOpacity>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 12,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  title: {
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.4,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: -0.2,
  },
  input: {
    borderWidth: 1,
    borderRadius: 6,
    padding: 14,
    fontSize: 16,
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    borderWidth: 1,
    borderRadius: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 20,
    minWidth: 30,
    textAlign: 'center',
  },
  submitButton: {
    backgroundColor: '#FF9500',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 10,
    shadowColor: '#FF9500',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
    shadowOpacity: 0,
    elevation: 0,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  pickerButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 48,
  },
  pickerButtonText: {
    fontSize: 16,
    flex: 1,
  },
  pickerArrow: {
    fontSize: 12,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    pointerEvents: 'auto',
  },
  modalContent: {
    borderRadius: 20,
    padding: 24,
    width: '90%',
    maxHeight: '75%',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 20,
  },
  productList: {
    maxHeight: 350,
  },
  productItem: {
    padding: 18,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  productItemText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  productStock: {
    fontSize: 13,
    opacity: 0.8,
    fontWeight: '500',
  },
  modalCloseButton: {
    backgroundColor: '#FF3B30',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#FF3B30',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  modalCloseText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
});
