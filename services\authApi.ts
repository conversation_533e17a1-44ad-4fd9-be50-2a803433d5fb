/**
 * Authentication API Service
 * Handles user authentication, token management, and user operations
 */

import { LoginCredentials, User } from '@/types/User';
import { apiClient, ApiError, tokenManager } from './api';

export interface LoginResponse {
  success: boolean;
  user: User;
  token: string;
  refreshToken: string;
  message?: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  token: string;
  refreshToken: string;
}

export interface LogoutResponse {
  success: boolean;
  message: string;
}

export interface UserProfileResponse {
  success: boolean;
  user: User;
}

// Auth API endpoints
const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  PROFILE: '/auth/profile',
  USERS: '/auth/users',
} as const;

/**
 * Authentication API Service Class
 */
export class AuthApiService {
  /**
   * Login user with credentials
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      console.log('🔐 API: Attempting login for user:', credentials.username);

      const response = await apiClient.post<LoginResponse>(AUTH_ENDPOINTS.LOGIN, {
        username: credentials.username.trim(),
        password: credentials.password,
      });

      if (response.success && response.token && response.refreshToken) {
        // Save tokens
        await tokenManager.saveTokens(response.token, response.refreshToken);
        console.log('✅ API: Login successful, tokens saved');
        return response;
      } else {
        throw new ApiError(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('❌ API: Login error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Handle network errors or other issues
      throw new ApiError('Network error. Please check your connection.');
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<LogoutResponse> {
    try {
      console.log('🚪 API: Attempting logout');

      const response = await apiClient.post<LogoutResponse>(AUTH_ENDPOINTS.LOGOUT);
      
      // Clear tokens regardless of response
      await tokenManager.clearTokens();
      console.log('✅ API: Logout successful, tokens cleared');
      
      return response;
    } catch (error) {
      console.error('❌ API: Logout error:', error);
      
      // Clear tokens even if logout request fails
      await tokenManager.clearTokens();
      
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const currentRefreshToken = tokenManager.getRefreshToken();
      
      if (!currentRefreshToken) {
        throw new ApiError('No refresh token available');
      }

      console.log('🔄 API: Refreshing token');

      const response = await apiClient.post<RefreshTokenResponse>(AUTH_ENDPOINTS.REFRESH, {
        refreshToken: currentRefreshToken,
      });

      if (response.success && response.token && response.refreshToken) {
        // Save new tokens
        await tokenManager.saveTokens(response.token, response.refreshToken);
        console.log('✅ API: Token refreshed successfully');
        return response;
      } else {
        throw new ApiError('Token refresh failed');
      }
    } catch (error) {
      console.error('❌ API: Token refresh error:', error);
      
      // Clear tokens if refresh fails
      await tokenManager.clearTokens();
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Token refresh failed');
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(): Promise<UserProfileResponse> {
    try {
      console.log('👤 API: Fetching user profile');

      const response = await apiClient.get<UserProfileResponse>(AUTH_ENDPOINTS.PROFILE);

      if (response.success && response.user) {
        console.log('✅ API: Profile fetched successfully');
        return response;
      } else {
        throw new ApiError('Failed to fetch profile');
      }
    } catch (error) {
      console.error('❌ API: Profile fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch profile');
    }
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers(): Promise<User[]> {
    try {
      console.log('👥 API: Fetching all users');

      const response = await apiClient.get<{ success: boolean; users: User[] }>(AUTH_ENDPOINTS.USERS);

      if (response.success && response.users) {
        console.log('✅ API: Users fetched successfully');
        return response.users;
      } else {
        throw new ApiError('Failed to fetch users');
      }
    } catch (error) {
      console.error('❌ API: Users fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch users');
    }
  }

  /**
   * Validate current token
   */
  async validateToken(): Promise<boolean> {
    try {
      const token = tokenManager.getToken();
      
      if (!token) {
        return false;
      }

      // Try to fetch profile to validate token
      await this.getProfile();
      return true;
    } catch (error) {
      console.log('🔍 API: Token validation failed, attempting refresh');
      
      try {
        // Try to refresh token
        await this.refreshToken();
        return true;
      } catch (refreshError) {
        console.log('❌ API: Token refresh failed, user needs to login');
        return false;
      }
    }
  }

  /**
   * Initialize auth state from stored tokens
   */
  async initializeAuth(): Promise<User | null> {
    try {
      console.log('🔄 API: Initializing auth state');

      // Load tokens from storage
      await tokenManager.loadTokens();

      const token = tokenManager.getToken();
      if (!token) {
        console.log('❌ API: No token found');
        return null;
      }

      // Validate token and get user profile
      const isValid = await this.validateToken();
      if (!isValid) {
        console.log('❌ API: Token validation failed');
        return null;
      }

      const profileResponse = await this.getProfile();
      console.log('✅ API: Auth initialized successfully');
      return profileResponse.user;
    } catch (error) {
      console.error('❌ API: Auth initialization error:', error);
      await tokenManager.clearTokens();
      return null;
    }
  }
}

// Create and export auth API service instance
export const authApi = new AuthApiService();

// Types are already exported above
