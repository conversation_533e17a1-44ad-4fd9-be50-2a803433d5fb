import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { LineChart } from 'react-native-chart-kit';

interface ProfitLossData {
  month: string;
  revenue: number;
  cost: number;
  profit: number;
}

interface ProfitLossChartProps {
  data: ProfitLossData[];
}

export function ProfitLossChart({ data }: ProfitLossChartProps) {
  const screenWidth = Dimensions.get('window').width;
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  // Prepare chart data
  const chartData = {
    labels: data.map(item => item.month),
    datasets: [
      {
        data: data.map(item => item.profit),
        color: (opacity = 1) => `rgba(52, 199, 89, ${opacity})`, // Green for profit
        strokeWidth: 3,
      },
      {
        data: data.map(item => item.revenue),
        color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`, // Blue for revenue
        strokeWidth: 2,
      },
      {
        data: data.map(item => item.cost),
        color: (opacity = 1) => `rgba(255, 149, 0, ${opacity})`, // Orange for cost
        strokeWidth: 2,
      },
    ],
    legend: ['Kar/Zarar', 'Gelir', 'Maliyet'],
  };

  const chartConfig = {
    backgroundColor: backgroundColor,
    backgroundGradientFrom: backgroundColor,
    backgroundGradientTo: backgroundColor,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
    labelColor: (opacity = 1) => textColor,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#ffa726',
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: `rgba(0, 122, 255, 0.2)`,
      strokeWidth: 1,
    },
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        📈 Kar/Zarar Trendi
      </ThemedText>
      
      <ThemedView style={styles.chartContainer}>
        <LineChart
          data={chartData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          withInnerLines={true}
          withOuterLines={true}
          withVerticalLines={true}
          withHorizontalLines={true}
          withDots={true}
          withShadow={false}
          withVerticalLabels={true}
          withHorizontalLabels={true}
        />
      </ThemedView>

      {/* Legend */}
      <ThemedView style={styles.legend}>
        <ThemedView style={styles.legendItem}>
          <ThemedView style={[styles.legendColor, { backgroundColor: '#34C759' }]} />
          <ThemedText style={styles.legendText}>Kar/Zarar</ThemedText>
        </ThemedView>
        <ThemedView style={styles.legendItem}>
          <ThemedView style={[styles.legendColor, { backgroundColor: '#007AFF' }]} />
          <ThemedText style={styles.legendText}>Gelir</ThemedText>
        </ThemedView>
        <ThemedView style={styles.legendItem}>
          <ThemedView style={[styles.legendColor, { backgroundColor: '#FF9500' }]} />
          <ThemedText style={styles.legendText}>Maliyet</ThemedText>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 30,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.1)',
  },
  title: {
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 18,
    fontWeight: '700',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  chart: {
    borderRadius: 16,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
