# Backend Migration Guide

Bu rehber, uygulamanızı local storage'dan backend API'ye geçirmek için gerekli adımları açıklar.

## 🔄 Migration Adımları

### 1. Environment Variables Ayarlayın

`.env.example` dosyasını `.env` olarak kopyalayın ve backend URL'inizi ayarlayın:

```bash
cp .env.example .env
```

`.env` dosyasında backend URL'inizi güncelleyin:
```
EXPO_PUBLIC_API_URL=http://your-backend-url.com/api
```

### 2. Hook'ları Değiştirin

Mevcut hook'ları yeni backend entegreli hook'larla de<PERSON>:

#### Auth Hook
```typescript
// Eski
import { useAuth } from '@/hooks/useAuth';

// Yeni
import { useAuth } from '@/hooks/useAuthNew';
```

#### Products Hook
```typescript
// Eski
import { useProducts } from '@/hooks/useProducts';

// Yeni
import { useProducts } from '@/hooks/useProductsNew';
```

#### Orders Hook
```typescript
// Eski
import { useOrders } from '@/hooks/useOrders';

// Yeni
import { useOrders } from '@/hooks/useOrdersNew';
```

#### Costs Hook
```typescript
// Eski
import { useCosts } from '@/hooks/useCosts';

// Yeni
import { useCosts } from '@/hooks/useCostsNew';
```

### 3. Backend API Endpoints

Backend'inizde aşağıdaki endpoint'leri implement etmeniz gerekiyor:

#### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
GET  /api/auth/users
```

#### Products Endpoints
```
GET    /api/products
POST   /api/products
GET    /api/products/:id
PUT    /api/products/:id
DELETE /api/products/:id
PUT    /api/products/:id/stock
GET    /api/products/search?q=query
```

#### Orders Endpoints
```
GET    /api/orders
POST   /api/orders
GET    /api/orders/:id
PUT    /api/orders/:id
DELETE /api/orders/:id
PUT    /api/orders/:id/status
GET    /api/orders/status/:status
GET    /api/orders/search?q=query
```

#### Costs Endpoints
```
GET    /api/costs
POST   /api/costs
GET    /api/costs/:id
PUT    /api/costs/:id
DELETE /api/costs/:id
GET    /api/costs/summary
GET    /api/costs/product/:productName
GET    /api/costs/search?q=query
```

### 4. JWT Token Authentication

Backend'iniz JWT token authentication kullanmalı:

- Login başarılı olduğunda `token` ve `refreshToken` döndürün
- Her API isteğinde `Authorization: Bearer <token>` header'ını kontrol edin
- Token expire olduğunda 401 status code döndürün
- Refresh token endpoint'i ile token yenileme sağlayın

### 5. Data Models

Backend'inizde aşağıdaki data model'leri kullanın:

#### User Model
```typescript
interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: 'admin' | 'user';
  isActive: boolean;
  createdAt: Date;
  lastLogin: Date;
  permissions: UserPermissions;
}
```

#### Product Model
```typescript
interface Product {
  id: string;
  name: string;
  stock: number;
  description?: string;
  createdAt: Date;
}
```

#### Order Model
```typescript
interface Order {
  id: string;
  productId?: string;
  productName: string;
  customerName: string;
  quantity: number;
  orderDate: Date;
  status: 'pending' | 'completed' | 'cancelled';
  salePrice?: number;
}
```

#### Cost Model
```typescript
interface Cost {
  id: string;
  productName: string;
  quantity: number;
  essencePrice: number;
  essenceAmount: number;
  essenceUsed: number;
  alcoholPrice: number;
  alcoholUsed: number;
  bottlePrice: number;
  unitCost: number;
  totalCost: number;
  createdAt: Date;
}
```

### 6. Error Handling

Backend'iniz aşağıdaki error format'ını kullanmalı:

```json
{
  "success": false,
  "message": "Error message",
  "code": "ERROR_CODE",
  "status": 400
}
```

### 7. Response Format

Tüm API response'ları aşağıdaki format'ta olmalı:

```json
{
  "success": true,
  "data": { ... },
  "message": "Success message"
}
```

### 8. Real-time Synchronization

Uygulama her 30 saniyede bir otomatik olarak backend'den veri çeker. 
Daha hızlı real-time sync için WebSocket veya Server-Sent Events kullanabilirsiniz.

### 9. Migration Script

Mevcut local data'yı backend'e migrate etmek için migration script'i yazabilirsiniz:

```typescript
// Migration helper (örnek)
async function migrateLocalDataToBackend() {
  // Local storage'dan veri oku
  const localProducts = await AsyncStorage.getItem('@inventory_products');
  const localOrders = await AsyncStorage.getItem('@inventory_orders');
  const localCosts = await AsyncStorage.getItem('@inventory_costs');
  
  // Backend'e gönder
  if (localProducts) {
    const products = JSON.parse(localProducts);
    for (const product of products) {
      await productsApi.createProduct(product);
    }
  }
  
  // Diğer veriler için de aynı işlemi tekrarla
}
```

### 10. Testing

Backend entegrasyonunu test etmek için:

1. Backend server'ınızı çalıştırın
2. `.env` dosyasında doğru URL'i ayarlayın
3. Uygulamayı başlatın
4. Login işlemini test edin
5. CRUD işlemlerini test edin
6. Multi-device sync'i test edin

## 🚀 Deployment

Production'a deploy ederken:

1. Production backend URL'ini ayarlayın
2. HTTPS kullanın
3. Rate limiting ekleyin
4. Database backup'larını ayarlayın
5. Monitoring ve logging ekleyin

## 📱 Multi-Device Support

Backend entegrasyonu ile:

- ✅ Aynı hesap farklı cihazlarda kullanılabilir
- ✅ Veriler real-time senkronize olur
- ✅ Offline durumda local cache kullanılır
- ✅ Online olduğunda otomatik sync yapılır

## 🔐 Security

- JWT token'lar güvenli şekilde saklanır
- API istekleri HTTPS üzerinden yapılır
- Token expiration ve refresh mekanizması vardır
- User permissions kontrol edilir
