/**
 * Costs API Service
 * Handles cost CRUD operations with backend synchronization
 */

import { apiClient, ApiError } from './api';
import { Cost, CostSummary } from '@/types/Cost';

export interface CostsResponse {
  success: boolean;
  costs: Cost[];
  total?: number;
  page?: number;
  limit?: number;
}

export interface CostResponse {
  success: boolean;
  cost: Cost;
  message?: string;
}

export interface DeleteCostResponse {
  success: boolean;
  message: string;
}

export interface CostSummaryResponse {
  success: boolean;
  summary: CostSummary;
}

export interface CreateCostRequest {
  productName: string;
  quantity: number;
  essencePrice: number;
  essenceAmount: number;
  essenceUsed: number;
  alcoholPrice: number;
  alcoholUsed: number;
  bottlePrice: number;
}

export interface UpdateCostRequest {
  id: string;
  productName?: string;
  quantity?: number;
  essencePrice?: number;
  essenceAmount?: number;
  essenceUsed?: number;
  alcoholPrice?: number;
  alcoholUsed?: number;
  bottlePrice?: number;
}

// Costs API endpoints
const COSTS_ENDPOINTS = {
  LIST: '/costs',
  CREATE: '/costs',
  GET: (id: string) => `/costs/${id}`,
  UPDATE: (id: string) => `/costs/${id}`,
  DELETE: (id: string) => `/costs/${id}`,
  SUMMARY: '/costs/summary',
  BY_PRODUCT: (productName: string) => `/costs/product/${encodeURIComponent(productName)}`,
  SEARCH: '/costs/search',
} as const;

/**
 * Costs API Service Class
 */
export class CostsApiService {
  /**
   * Get all costs
   */
  async getCosts(page = 1, limit = 100): Promise<Cost[]> {
    try {
      console.log('💰 API: Fetching costs');

      const response = await apiClient.get<CostsResponse>(
        `${COSTS_ENDPOINTS.LIST}?page=${page}&limit=${limit}`
      );

      if (response.success && response.costs) {
        console.log(`✅ API: Fetched ${response.costs.length} costs`);
        
        // Convert date strings to Date objects
        const costs = response.costs.map(cost => ({
          ...cost,
          createdAt: new Date(cost.createdAt),
        }));
        
        return costs;
      } else {
        throw new ApiError('Failed to fetch costs');
      }
    } catch (error) {
      console.error('❌ API: Costs fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch costs');
    }
  }

  /**
   * Get single cost by ID
   */
  async getCost(id: string): Promise<Cost> {
    try {
      console.log('💰 API: Fetching cost:', id);

      const response = await apiClient.get<CostResponse>(COSTS_ENDPOINTS.GET(id));

      if (response.success && response.cost) {
        console.log('✅ API: Cost fetched successfully');
        
        // Convert date string to Date object
        const cost = {
          ...response.cost,
          createdAt: new Date(response.cost.createdAt),
        };
        
        return cost;
      } else {
        throw new ApiError('Cost not found');
      }
    } catch (error) {
      console.error('❌ API: Cost fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch cost');
    }
  }

  /**
   * Create new cost entry
   */
  async createCost(costData: CreateCostRequest): Promise<Cost> {
    try {
      console.log('💰 API: Creating cost for:', costData.productName);

      const response = await apiClient.post<CostResponse>(COSTS_ENDPOINTS.CREATE, {
        productName: costData.productName.trim(),
        quantity: costData.quantity,
        essencePrice: costData.essencePrice,
        essenceAmount: costData.essenceAmount,
        essenceUsed: costData.essenceUsed,
        alcoholPrice: costData.alcoholPrice,
        alcoholUsed: costData.alcoholUsed,
        bottlePrice: costData.bottlePrice,
      });

      if (response.success && response.cost) {
        console.log('✅ API: Cost created successfully');
        
        // Convert date string to Date object
        const cost = {
          ...response.cost,
          createdAt: new Date(response.cost.createdAt),
        };
        
        return cost;
      } else {
        throw new ApiError(response.message || 'Failed to create cost');
      }
    } catch (error) {
      console.error('❌ API: Cost creation error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to create cost');
    }
  }

  /**
   * Update existing cost
   */
  async updateCost(costData: UpdateCostRequest): Promise<Cost> {
    try {
      console.log('💰 API: Updating cost:', costData.id);

      const updateData: any = {};
      if (costData.productName !== undefined) updateData.productName = costData.productName.trim();
      if (costData.quantity !== undefined) updateData.quantity = costData.quantity;
      if (costData.essencePrice !== undefined) updateData.essencePrice = costData.essencePrice;
      if (costData.essenceAmount !== undefined) updateData.essenceAmount = costData.essenceAmount;
      if (costData.essenceUsed !== undefined) updateData.essenceUsed = costData.essenceUsed;
      if (costData.alcoholPrice !== undefined) updateData.alcoholPrice = costData.alcoholPrice;
      if (costData.alcoholUsed !== undefined) updateData.alcoholUsed = costData.alcoholUsed;
      if (costData.bottlePrice !== undefined) updateData.bottlePrice = costData.bottlePrice;

      const response = await apiClient.put<CostResponse>(
        COSTS_ENDPOINTS.UPDATE(costData.id),
        updateData
      );

      if (response.success && response.cost) {
        console.log('✅ API: Cost updated successfully');
        
        // Convert date string to Date object
        const cost = {
          ...response.cost,
          createdAt: new Date(response.cost.createdAt),
        };
        
        return cost;
      } else {
        throw new ApiError(response.message || 'Failed to update cost');
      }
    } catch (error) {
      console.error('❌ API: Cost update error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to update cost');
    }
  }

  /**
   * Delete cost
   */
  async deleteCost(id: string): Promise<void> {
    try {
      console.log('💰 API: Deleting cost:', id);

      const response = await apiClient.delete<DeleteCostResponse>(COSTS_ENDPOINTS.DELETE(id));

      if (response.success) {
        console.log('✅ API: Cost deleted successfully');
      } else {
        throw new ApiError(response.message || 'Failed to delete cost');
      }
    } catch (error) {
      console.error('❌ API: Cost deletion error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to delete cost');
    }
  }

  /**
   * Get cost summary
   */
  async getCostSummary(): Promise<CostSummary> {
    try {
      console.log('💰 API: Fetching cost summary');

      const response = await apiClient.get<CostSummaryResponse>(COSTS_ENDPOINTS.SUMMARY);

      if (response.success && response.summary) {
        console.log('✅ API: Cost summary fetched successfully');
        return response.summary;
      } else {
        throw new ApiError('Failed to fetch cost summary');
      }
    } catch (error) {
      console.error('❌ API: Cost summary fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch cost summary');
    }
  }

  /**
   * Get costs by product name
   */
  async getCostsByProduct(productName: string): Promise<Cost[]> {
    try {
      console.log('💰 API: Fetching costs for product:', productName);

      const response = await apiClient.get<CostsResponse>(COSTS_ENDPOINTS.BY_PRODUCT(productName));

      if (response.success && response.costs) {
        console.log(`✅ API: Found ${response.costs.length} costs for product ${productName}`);
        
        // Convert date strings to Date objects
        const costs = response.costs.map(cost => ({
          ...cost,
          createdAt: new Date(cost.createdAt),
        }));
        
        return costs;
      } else {
        return [];
      }
    } catch (error) {
      console.error('❌ API: Costs by product fetch error:', error);
      return [];
    }
  }

  /**
   * Search costs by product name
   */
  async searchCosts(query: string): Promise<Cost[]> {
    try {
      console.log('💰 API: Searching costs:', query);

      const response = await apiClient.get<CostsResponse>(
        `${COSTS_ENDPOINTS.SEARCH}?q=${encodeURIComponent(query)}`
      );

      if (response.success && response.costs) {
        console.log(`✅ API: Found ${response.costs.length} costs`);
        
        // Convert date strings to Date objects
        const costs = response.costs.map(cost => ({
          ...cost,
          createdAt: new Date(cost.createdAt),
        }));
        
        return costs;
      } else {
        return [];
      }
    } catch (error) {
      console.error('❌ API: Cost search error:', error);
      return [];
    }
  }
}

// Create and export costs API service instance
export const costsApi = new CostsApiService();

// Export types
export type {
  CostsResponse,
  CostResponse,
  DeleteCostResponse,
  CostSummaryResponse,
  CreateCostRequest,
  UpdateCostRequest,
};
