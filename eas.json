{"cli": {"version": "16.10.1", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"android": {"buildType": "app-bundle"}}, "production-apk": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_SUPABASE_URL": "https://nmaokkcclboszurxjgog.supabase.co", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5tYW9ra2NjbGJvc3p1cnhqZ29nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNzcxNDYsImV4cCI6MjA2NTc1MzE0Nn0.Uhpiux4RG4nZlH3iPj9QH8aFpqhFR5VpewJjhVnu1V0"}}, "standalone-apk": {"distribution": "internal", "android": {"buildType": "apk"}, "env": {"SUPABASE_URL": "https://nmaokkcclboszurxjgog.supabase.co"}}}, "submit": {"production": {}}}