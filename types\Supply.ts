export type SupplyStatus = 'ordered' | 'shipped' | 'delivered';

export interface Supply {
  id: string;
  itemName: string;
  category: 'essence' | 'alcohol' | 'bottle' | 'packaging' | 'other';
  supplier: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  status: SupplyStatus;
  notes?: string;
  createdAt?: Date;
}
