import { AnimatedButton } from '@/components/AnimatedButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import React, { useState } from 'react';
import { Alert, StyleSheet, TextInput } from 'react-native';

interface ProductFormProps {
  onAddProduct: (name: string, stock: number) => Promise<void>;
}

export function ProductForm({ onAddProduct }: ProductFormProps) {
  const [name, setName] = useState('');
  const [stock, setStock] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'surface');
  const borderColor = useThemeColor({}, 'border');
  const placeholderColor = useThemeColor({}, 'textTertiary');

  const handleSubmit = async () => {
    // ZORUNLU ALAN KONTROLÜ - name
    if (!name || typeof name !== 'string' || name.trim() === '') {
      Alert.alert('Hata', 'Ürün adı zorunludur ve boş olamaz');
      return;
    }

    // ZORUNLU ALAN KONTROLÜ - stock
    const stockNumber = parseInt(stock);
    if (!stock || isNaN(stockNumber) || stockNumber < 0) {
      Alert.alert('Hata', 'Geçerli bir stok miktarı giriniz (0 veya daha büyük sayı)');
      return;
    }

    console.log('🔄 [FORM] Form gönderiliyor:', {
      name: name.trim(),
      stock: stockNumber,
      nameType: typeof name,
      stockType: typeof stockNumber
    });

    setIsSubmitting(true);
    try {
      await onAddProduct(name.trim(), stockNumber);
      setName('');
      setStock('');
      Alert.alert('Başarılı', 'Ürün başarıyla eklendi');
    } catch (error: any) {
      console.error('❌ [FORM] Ürün ekleme hatası:', error);
      Alert.alert('Hata', error.message || 'Ürün eklenirken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        Yeni Ürün Ekle
      </ThemedText>
      
      <ThemedView style={styles.formGroup}>
        <ThemedText style={styles.label}>Ürün Adı</ThemedText>
        <TextInput
          style={[
            styles.input,
            { 
              color: textColor, 
              backgroundColor: backgroundColor,
              borderColor: borderColor 
            }
          ]}
          value={name}
          onChangeText={setName}
          placeholder="Ürün adını giriniz"
          placeholderTextColor={placeholderColor}
          editable={!isSubmitting}
        />
      </ThemedView>

      <ThemedView style={styles.formGroup}>
        <ThemedText style={styles.label}>Stok Miktarı</ThemedText>
        <TextInput
          style={[
            styles.input,
            { 
              color: textColor, 
              backgroundColor: backgroundColor,
              borderColor: borderColor 
            }
          ]}
          value={stock}
          onChangeText={setStock}
          placeholder="Stok miktarını giriniz"
          placeholderTextColor={placeholderColor}
          keyboardType="numeric"
          editable={!isSubmitting}
        />
      </ThemedView>

      <AnimatedButton
        title={isSubmitting ? 'Ekleniyor...' : 'Ürün Ekle'}
        onPress={handleSubmit}
        disabled={isSubmitting}
        variant="primary"
        size="large"
        style={styles.submitButton}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  title: {
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.4,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: -0.2,
  },
  input: {
    borderWidth: 1,
    borderRadius: 6,
    padding: 14,
    fontSize: 16,
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  submitButton: {
    padding: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
    elevation: 0,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
});
