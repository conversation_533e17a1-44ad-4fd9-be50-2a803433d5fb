{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "veldae", "version": "1.3.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.veldae.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.veldae.app", "versionCode": 1, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "compileSdkVersion": 33, "targetSdkVersion": 33, "buildToolsVersion": "33.0.0"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "expo-build-properties"], "scheme": "veldae", "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "ac74f941-3779-4728-9c61-d40de80d9dd2"}, "EXPO_PUBLIC_SUPABASE_URL": "...", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "...", "EXPO_PUBLIC_ENV": "production", "EXPO_PUBLIC_API_TIMEOUT": 30000, "EXPO_PUBLIC_SYNC_INTERVAL": "300000", "EXPO_PUBLIC_DEBUG_API": "false", "EXPO_PUBLIC_DEBUG_AUTH": "false"}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/a1f6aecb-a3fb-44f2-8d4e-26b6b270e605"}}}