import { useState, useEffect } from 'react';
import { splashStore } from '@/store/splashStore';

export const useGlobalSplash = () => {
  // Local state to trigger re-renders
  const [isSplashFinished, setIsSplashFinished] = useState(
    splashStore.getIsSplashFinished()
  );

  useEffect(() => {
    console.log('🎬 useGlobalSplash initialized:', {
      isSplashFinished: splashStore.getIsSplashFinished(),
      debugInfo: splashStore.getDebugInfo()
    });

    // Subscribe to global state changes
    const unsubscribe = splashStore.subscribe(() => {
      const newState = splashStore.getIsSplashFinished();
      console.log('🎬 Global splash state changed:', newState);
      setIsSplashFinished(newState);
    });

    // Cleanup subscription
    return unsubscribe;
  }, []);

  const shouldShowSplash = (): boolean => {
    const should = !splashStore.getIsSplashFinished();
    console.log('🎬 Should show splash?', {
      should,
      isSplashFinished: splashStore.getIsSplashFinished(),
      debugInfo: splashStore.getDebugInfo()
    });
    return should;
  };

  const markSplashFinished = (): void => {
    console.log('🎬 Marking splash as finished');
    splashStore.setSplashFinished(true);
  };

  const resetSplash = (): void => {
    console.log('🎬 Resetting splash');
    splashStore.reset();
  };

  return {
    isSplashFinished,
    shouldShowSplash,
    markSplashFinished,
    resetSplash,
    debugInfo: splashStore.getDebugInfo()
  };
};
