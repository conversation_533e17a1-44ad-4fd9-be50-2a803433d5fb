import { useCallback, useRef } from 'react';

export interface OptimisticItem {
  id: string;
  isOptimistic?: boolean;
  [key: string]: any;
}

interface UseOptimisticUpdateOptions<T extends OptimisticItem> {
  onError?: (error: Error) => void;
}

export function useOptimisticUpdate<T extends OptimisticItem>(options: UseOptimisticUpdateOptions<T> = {}) {
  const pendingItems = useRef<Map<string, T>>(new Map());
  const tempIdCounter = useRef(0);

  const generateTempId = useCallback(() => {
    return `temp_${Date.now()}_${tempIdCounter.current++}`;
  }, []);

  const addOptimisticItem = useCallback((item: Omit<T, 'id' | 'isOptimistic'>): T => {
    const tempId = generateTempId();
    const optimisticItem = {
      ...item,
      id: tempId,
      isOptimistic: true
    } as T;
    
    pendingItems.current.set(tempId, optimisticItem);
    return optimisticItem;
  }, [generateTempId]);

  const confirmOptimisticItem = useCallback((tempId: string, realItem: T) => {
    pendingItems.current.delete(tempId);
  }, []);

  const removeOptimisticItem = useCallback((tempId: string, error: Error) => {
    pendingItems.current.delete(tempId);
    if (options.onError) {
      options.onError(error);
    }
  }, [options]);

  const isPending = useCallback((id: string) => {
    return pendingItems.current.has(id);
  }, []);

  return {
    addOptimisticItem,
    confirmOptimisticItem,
    removeOptimisticItem,
    isPending,
    pendingItems: pendingItems.current
  };
} 