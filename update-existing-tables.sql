-- Update Existing Tables for Inventory Management App
-- Run this in Supabase SQL Editor

-- 1. Add missing columns to existing tables (if they don't exist)

-- Add user_id to products table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'products' AND column_name = 'user_id') THEN
        ALTER TABLE products ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add user_id to orders table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'orders' AND column_name = 'user_id') THEN
        ALTER TABLE orders ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- 2. Create costs table if it doesn't exist
CREATE TABLE IF NOT EXISTS costs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_name TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  essence_price DECIMAL(10,2) NOT NULL DEFAULT 0,
  essence_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  essence_used DECIMAL(10,2) NOT NULL DEFAULT 0,
  alcohol_price DECIMAL(10,2) NOT NULL DEFAULT 0,
  alcohol_used DECIMAL(10,2) NOT NULL DEFAULT 0,
  bottle_price DECIMAL(10,2) NOT NULL DEFAULT 0,
  unit_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- 3. Enable Row Level Security (RLS) if not already enabled
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE costs ENABLE ROW LEVEL SECURITY;

-- 4. Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view own products" ON products;
DROP POLICY IF EXISTS "Users can insert own products" ON products;
DROP POLICY IF EXISTS "Users can update own products" ON products;
DROP POLICY IF EXISTS "Users can delete own products" ON products;

DROP POLICY IF EXISTS "Users can view own orders" ON orders;
DROP POLICY IF EXISTS "Users can insert own orders" ON orders;
DROP POLICY IF EXISTS "Users can update own orders" ON orders;
DROP POLICY IF EXISTS "Users can delete own orders" ON orders;

DROP POLICY IF EXISTS "Users can view own costs" ON costs;
DROP POLICY IF EXISTS "Users can insert own costs" ON costs;
DROP POLICY IF EXISTS "Users can update own costs" ON costs;
DROP POLICY IF EXISTS "Users can delete own costs" ON costs;

-- 5. Create new RLS Policies
-- Products policies
CREATE POLICY "Users can view own products" ON products FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own products" ON products FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own products" ON products FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own products" ON products FOR DELETE USING (auth.uid() = user_id);

-- Orders policies
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own orders" ON orders FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own orders" ON orders FOR DELETE USING (auth.uid() = user_id);

-- Costs policies
CREATE POLICY "Users can view own costs" ON costs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own costs" ON costs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own costs" ON costs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own costs" ON costs FOR DELETE USING (auth.uid() = user_id);

-- 6. Create indexes for better performance (if they don't exist)
CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_costs_user_id ON costs(user_id);
CREATE INDEX IF NOT EXISTS idx_costs_created_at ON costs(created_at);

-- Setup complete! 🎉
-- This script safely updates existing tables and adds missing columns
-- Next steps:
-- 1. Create test users in Authentication > Users:
--    - Email: <EMAIL>, Password: Gencer103
--    - Email: <EMAIL>, Password: Kurt123
-- 2. Test the app at http://localhost:8082
