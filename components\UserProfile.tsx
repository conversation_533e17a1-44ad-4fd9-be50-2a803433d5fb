import { AnimatedButton } from '@/components/AnimatedButton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useTheme } from '@/contexts/ThemeContext';
import { useSupabaseAuthForced as useAuth } from '@/hooks/useSupabaseAuthForced';
import { useThemeColor } from '@/hooks/useThemeColor';
import { ROLE_DESCRIPTIONS, ROLE_LABELS } from '@/types/User';
import React from 'react';
import { Alert, ScrollView, StyleSheet, Switch } from 'react-native';

interface UserProfileProps {
  onClose?: () => void;
}

export function UserProfile({ onClose }: UserProfileProps) {
  const { user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const borderColor = useThemeColor({ light: '#E5E5E7', dark: '#38383A' }, 'border');

  if (!user) return null;

  const handleThemeToggle = (value: boolean) => {
    console.log('🎨 Theme toggle requested:', value ? 'dark' : 'light');
    toggleTheme();
  };

  const handleLogout = () => {
    Alert.alert(
      'Çıkış Yap',
      'Oturumu kapatmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: () => {
            logout();
            onClose?.();
          }
        },
      ]
    );
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return '👑';
      case 'manager': return '👔';
      case 'employee': return '👨‍💼';
      case 'viewer': return '👁️';
      default: return '👤';
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? '#34C759' : '#FF3B30';
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          Kullanıcı Profili
        </ThemedText>
        {onClose && (
          <AnimatedButton
            title="✕"
            onPress={onClose}
            variant="secondary"
            size="small"
            style={styles.closeButton}
          />
        )}
      </ThemedView>

      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
      >
        <ThemedView style={styles.scrollPadding}>

      {/* User Info */}
      <ThemedView style={styles.userInfo}>
        <ThemedView style={styles.avatarContainer}>
          <ThemedText style={styles.avatar}>
            {getRoleIcon(user.role)}
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.userDetails}>
          <ThemedText style={styles.fullName}>{user.fullName}</ThemedText>
          <ThemedText style={styles.username}>@{user.username}</ThemedText>
          <ThemedText style={styles.email}>{user.email}</ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Role & Status */}
      <ThemedView style={styles.roleSection}>
        <ThemedView style={styles.roleCard}>
          <ThemedText style={styles.roleLabel}>Rol</ThemedText>
          <ThemedText style={styles.roleValue}>
            {getRoleIcon(user.role)} {ROLE_LABELS[user.role]}
          </ThemedText>
          <ThemedText style={styles.roleDescription}>
            {ROLE_DESCRIPTIONS[user.role]}
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.statusCard}>
          <ThemedText style={styles.statusLabel}>Durum</ThemedText>
          <ThemedView style={styles.statusContainer}>
            <ThemedView 
              style={[styles.statusDot, { backgroundColor: getStatusColor(user.isActive) }]} 
            />
            <ThemedText style={[styles.statusText, { color: getStatusColor(user.isActive) }]}>
              {user.isActive ? 'Aktif' : 'Pasif'}
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      {/* Theme Settings */}
      <ThemedView style={styles.themeSection}>
        <ThemedText style={styles.sectionTitle}>Tema Ayarları</ThemedText>

        <ThemedView style={[styles.themeItem, { borderColor }]}>
          <ThemedView style={styles.themeInfo}>
            <ThemedText style={styles.themeLabel}>🌙 Karanlık Mod</ThemedText>
            <ThemedText style={styles.themeDescription}>
              Gözlerinizi korumak için karanlık tema kullanın
            </ThemedText>
          </ThemedView>
          <Switch
            value={isDarkMode}
            onValueChange={handleThemeToggle}
            trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
            thumbColor={isDarkMode ? '#FFFFFF' : '#FFFFFF'}
            ios_backgroundColor="#E5E5EA"
          />
        </ThemedView>
      </ThemedView>

      {/* Activity Info */}
      <ThemedView style={styles.activitySection}>
        <ThemedText style={styles.sectionTitle}>Hesap Bilgileri</ThemedText>
        
        <ThemedView style={styles.activityItem}>
          <ThemedText style={styles.activityLabel}>Hesap Oluşturma</ThemedText>
          <ThemedText style={styles.activityValue}>
            {formatDate(user.createdAt)}
          </ThemedText>
        </ThemedView>

        {user.lastLogin && (
          <ThemedView style={styles.activityItem}>
            <ThemedText style={styles.activityLabel}>Son Giriş</ThemedText>
            <ThemedText style={styles.activityValue}>
              {formatDate(user.lastLogin)}
            </ThemedText>
          </ThemedView>
        )}
      </ThemedView>

      {/* Permissions Summary */}
      <ThemedView style={styles.permissionsSection}>
        <ThemedText style={styles.sectionTitle}>Yetkiler Özeti</ThemedText>
        
        <ThemedView style={styles.permissionsGrid}>
          <ThemedView style={styles.permissionItem}>
            <ThemedText style={styles.permissionIcon}>📦</ThemedText>
            <ThemedText style={styles.permissionText}>
              Ürünler: {user.permissions.canAddProducts ? 'Tam Yetki' : 
                       user.permissions.canViewProducts ? 'Görüntüleme' : 'Yetki Yok'}
            </ThemedText>
          </ThemedView>

          <ThemedView style={styles.permissionItem}>
            <ThemedText style={styles.permissionIcon}>🛒</ThemedText>
            <ThemedText style={styles.permissionText}>
              Siparişler: {user.permissions.canCreateOrders ? 'Tam Yetki' : 
                          user.permissions.canViewOrders ? 'Görüntüleme' : 'Yetki Yok'}
            </ThemedText>
          </ThemedView>

          <ThemedView style={styles.permissionItem}>
            <ThemedText style={styles.permissionIcon}>💰</ThemedText>
            <ThemedText style={styles.permissionText}>
              Maliyetler: {user.permissions.canAddCosts ? 'Tam Yetki' : 
                          user.permissions.canViewCosts ? 'Görüntüleme' : 'Yetki Yok'}
            </ThemedText>
          </ThemedView>

          <ThemedView style={styles.permissionItem}>
            <ThemedText style={styles.permissionIcon}>📊</ThemedText>
            <ThemedText style={styles.permissionText}>
              Raporlar: {user.permissions.canExportReports ? 'Tam Yetki' : 
                        user.permissions.canViewReports ? 'Görüntüleme' : 'Yetki Yok'}
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

        {/* Logout Button */}
        <ThemedView style={styles.actions}>
          <AnimatedButton
            title="🚪 Çıkış Yap"
            onPress={handleLogout}
            variant="danger"
            size="large"
            style={styles.logoutButton}
          />
        </ThemedView>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80,
    flexGrow: 1,
  },
  scrollPadding: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
    padding: 20,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    fontSize: 48,
    textAlign: 'center',
  },
  userDetails: {
    flex: 1,
  },
  fullName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  username: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
    opacity: 0.6,
  },
  roleSection: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 30,
  },
  roleCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(52, 199, 89, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(52, 199, 89, 0.3)',
  },
  statusCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 149, 0, 0.3)',
  },
  roleLabel: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
    marginBottom: 4,
  },
  roleValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  roleDescription: {
    fontSize: 11,
    opacity: 0.6,
  },
  statusLabel: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  themeSection: {
    marginBottom: 30,
  },
  themeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  themeInfo: {
    flex: 1,
    marginRight: 16,
  },
  themeLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  themeDescription: {
    fontSize: 12,
    opacity: 0.7,
  },
  activitySection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  activityLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  activityValue: {
    fontSize: 14,
    opacity: 0.7,
  },
  permissionsSection: {
    marginBottom: 30,
  },
  permissionsGrid: {
    gap: 12,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  permissionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  permissionText: {
    fontSize: 14,
    flex: 1,
  },
  actions: {
    marginTop: 40,
    paddingTop: 20,
    borderTopWidth: 2,
    borderTopColor: 'rgba(255, 59, 48, 0.2)',
    backgroundColor: 'rgba(255, 59, 48, 0.05)',
    borderRadius: 12,
    padding: 16,
  },
  logoutButton: {
    marginTop: 10,
    shadowColor: '#FF3B30',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
});
