import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { router } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Image, StyleSheet, View } from 'react-native';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationFinish?: () => void;
}

export function SplashScreenImage({ onAnimationFinish }: SplashScreenProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    console.log('🎬 Image SplashScreen mounted - starting animations');
    
    // Start complex animation sequence
    const animationSequence = Animated.sequence([
      // Phase 1: Fade in and scale up
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 60,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),

      // Phase 2: Rotation and pulse (shorter)
      Animated.parallel([
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
          { iterations: 1 } // Only 1 pulse loop
        ),
      ]),

      // Phase 3: Final scale and fade out
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
    ]);

    animationSequence.start(() => {
      handleAnimationFinish();
    });

    // Fallback timer (shorter)
    const fallbackTimer = setTimeout(() => {
      console.log('🎬 Splash fallback timer triggered');
      handleAnimationFinish();
    }, 3000); // 3 seconds max

    return () => clearTimeout(fallbackTimer);
  }, []);

  const handleAnimationFinish = () => {
    console.log('🎬 Image splash animation completed');
    
    // Call callback if provided
    if (onAnimationFinish) {
      onAnimationFinish();
    }
    
    // Navigate to dashboard after animation (replace to avoid history stack)
    setTimeout(() => {
      console.log('🚀 Navigating to dashboard (replace mode)...');
      try {
        // Use replace to prevent splash from being in history stack
        router.replace('/dashboard');
        console.log('✅ Navigation to dashboard successful (no history entry)');
      } catch (error) {
        console.warn('⚠️ Dashboard navigation failed, trying root:', error);
        try {
          router.replace('/');
          console.log('✅ Fallback navigation to root successful');
        } catch (fallbackError) {
          console.warn('⚠️ All navigation failed:', fallbackError);
          // Don't try to reload on mobile - just log the error
          console.log('🔄 Navigation failed - staying on current screen');
        }
      }
    }, 200); // Shorter delay for faster navigation
  };

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <ThemedView style={styles.container}>
      {/* Background Gradient Effect */}
      <View style={styles.backgroundGradient} />
      
      {/* Animated Logo */}
      <Animated.View 
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [
              { scale: Animated.multiply(scaleAnim, pulseAnim) },
              { rotate: spin }
            ]
          }
        ]}
      >
        <Image
          source={require('@/assets/images/splash-icon.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>

      {/* App Title */}
      <Animated.View 
        style={[
          styles.titleContainer,
          { opacity: fadeAnim }
        ]}
      >
        <ThemedText style={styles.appTitle}>
          Inventory Manager
        </ThemedText>
        <ThemedText style={styles.appSubtitle}>
          Stok Yönetim Sistemi
        </ThemedText>
      </Animated.View>

      {/* Loading Indicator */}
      <Animated.View 
        style={[
          styles.loadingContainer,
          { opacity: fadeAnim }
        ]}
      >
        <View style={styles.loadingDots}>
          <Animated.View style={[styles.dot, { transform: [{ scale: pulseAnim }] }]} />
          <Animated.View style={[styles.dot, { transform: [{ scale: pulseAnim }] }]} />
          <Animated.View style={[styles.dot, { transform: [{ scale: pulseAnim }] }]} />
        </View>
        <ThemedText style={styles.loadingText}>
          Yükleniyor...
        </ThemedText>
      </Animated.View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  backgroundGradient: {
    flex: 1,
    backgroundColor: '#667eea',
    opacity: 0.9,
  },
  logoContainer: {
    width: width * 0.4,
    height: width * 0.4,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    borderRadius: (width * 0.4) / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 20,
    elevation: 10,
  },
  logo: {
    width: '70%',
    height: '70%',
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  appSubtitle: {
    fontSize: 16,
    color: '#e0e0e0',
    textAlign: 'center',
    opacity: 0.9,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 80,
  },
  loadingDots: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ffffff',
    marginHorizontal: 4,
  },
  loadingText: {
    fontSize: 14,
    color: '#e0e0e0',
    opacity: 0.8,
  },
});
