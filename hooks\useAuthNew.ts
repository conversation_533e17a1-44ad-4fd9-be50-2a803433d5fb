/**
 * New Authentication Hook with Backend Integration
 * Handles multi-user authentication with JWT tokens and backend synchronization
 */

import { ApiError } from '@/services/api';
import { authApi } from '@/services/authApi';
import { AuthState, LoginCredentials, User } from '@/types/User';
import { useCallback, useEffect, useState } from 'react';

// Global auth state management
let globalAuthInitialized = false;
let globalAuthState: AuthState | null = null;
let globalAuthListeners: Set<(state: AuthState) => void> = new Set();

// Global auth state management functions
const setGlobalAuthState = (newState: AuthState) => {
  globalAuthState = newState;
  globalAuthListeners.forEach(listener => {
    try {
      listener(newState);
    } catch (error) {
      if (__DEV__) console.error('Auth listener error:', error);
    }
  });
};

const addGlobalAuthListener = (listener: (state: AuthState) => void) => {
  globalAuthListeners.add(listener);
  return () => globalAuthListeners.delete(listener);
};

/**
 * Enhanced Authentication Hook with Backend Integration
 */
export function useAuthNew() {
  // Use global auth state if available, otherwise use default
  const [authState, setAuthState] = useState<AuthState>(() => {
    if (globalAuthState) {
      if (__DEV__) console.log('🔄 useAuth using existing global auth state');
      return globalAuthState;
    }
    if (__DEV__) console.log('🔄 useAuth creating new auth state');
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,
    };
  });

  /**
   * Initialize auth state from backend
   */
  const loadAuthState = useCallback(async () => {
    if (globalAuthInitialized) {
      if (__DEV__) console.log('🔄 Auth already initialized globally');
      return;
    }

    if (__DEV__) console.log('🔄 Initializing auth state from backend...');
    globalAuthInitialized = true;

    try {
      // Initialize auth from stored tokens
      const user = await authApi.initializeAuth();

      if (user) {
        const newAuthState = {
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        };

        setGlobalAuthState(newAuthState);
        setAuthState(newAuthState);

        if (__DEV__) console.log('✅ Auth initialized successfully:', user.username);
      } else {
        // No valid auth found
        const newAuthState = {
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        };

        setGlobalAuthState(newAuthState);
        setAuthState(newAuthState);

        if (__DEV__) console.log('❌ No valid auth found - showing login');
      }
    } catch (error) {
      console.error('❌ Auth initialization error:', error);

      const newAuthState = {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof ApiError ? error.message : 'Authentication failed',
      };

      setGlobalAuthState(newAuthState);
      setAuthState(newAuthState);
    }
  }, []);

  /**
   * Login with backend authentication
   */
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    if (__DEV__) console.log('🔐 Backend login attempt for:', credentials.username);

    try {
      // Set loading state
      const loadingState = {
        ...authState,
        isLoading: true,
        error: null,
      };
      setGlobalAuthState(loadingState);
      setAuthState(loadingState);

      // Attempt login via API
      const response = await authApi.login(credentials);

      if (response.success && response.user) {
        // Update last login
        const updatedUser = { ...response.user, lastLogin: new Date() };

        const newAuthState = {
          user: updatedUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        };

        setGlobalAuthState(newAuthState);
        setAuthState(newAuthState);

        if (__DEV__) console.log('✅ Backend login successful:', updatedUser.username);
        return true;
      } else {
        throw new ApiError(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('❌ Backend login error:', error);

      const errorMessage = error instanceof ApiError ? error.message : 'Login failed';
      const errorState = {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage,
      };

      setGlobalAuthState(errorState);
      setAuthState(errorState);

      return false;
    }
  }, [authState]);

  /**
   * Logout with backend cleanup
   */
  const logout = useCallback(async () => {
    if (__DEV__) console.log('🚪 Backend logout initiated');

    try {
      // Attempt logout via API
      await authApi.logout();
    } catch (error) {
      console.warn('❌ Backend logout error (continuing with local logout):', error);
    }

    // Clear local auth state
    const newAuthState = {
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    };

    setGlobalAuthState(newAuthState);
    setAuthState(newAuthState);

    if (__DEV__) console.log('✅ Logout completed');
  }, []);

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback((permission: keyof User['permissions']): boolean => {
    if (!authState.user || !authState.isAuthenticated) {
      return false;
    }

    return authState.user.permissions[permission] === true;
  }, [authState.user, authState.isAuthenticated]);

  /**
   * Get all users (admin only)
   */
  const getAllUsers = useCallback(async (): Promise<User[]> => {
    try {
      if (!hasPermission('canManageUsers')) {
        throw new ApiError('Bu işlem için yetkiniz yok');
      }

      return await authApi.getAllUsers();
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }, [hasPermission]);

  /**
   * Refresh user profile from backend
   */
  const refreshProfile = useCallback(async (): Promise<void> => {
    try {
      if (!authState.isAuthenticated) {
        return;
      }

      const profileResponse = await authApi.getProfile();
      
      if (profileResponse.success && profileResponse.user) {
        const newAuthState = {
          ...authState,
          user: profileResponse.user,
        };

        setGlobalAuthState(newAuthState);
        setAuthState(newAuthState);

        if (__DEV__) console.log('✅ Profile refreshed successfully');
      }
    } catch (error) {
      console.error('❌ Profile refresh error:', error);
      
      if (error instanceof ApiError && error.status === 401) {
        // Token expired, logout user
        await logout();
      }
    }
  }, [authState, logout]);

  // Global auth listener for state sync
  useEffect(() => {
    const unsubscribe = addGlobalAuthListener((newState) => {
      if (__DEV__) console.log('🔄 useAuth received global auth state update');
      setAuthState(newState);
    });

    return unsubscribe;
  }, []);

  // Initialize auth state only once globally
  useEffect(() => {
    if (!globalAuthInitialized) {
      if (__DEV__) console.log('🔄 useAuth initializing globally for the first time');
      loadAuthState();
    } else {
      if (__DEV__) console.log('🔄 useAuth using existing global state');
      if (globalAuthState) {
        setAuthState(globalAuthState);
      }
    }
  }, []); // Empty dependency array - only run once on mount

  // Debug auth state changes (only in development)
  useEffect(() => {
    if (__DEV__) console.log('🔄 useAuth state changed:', authState);
  }, [authState]);

  // Listen for logout events from API layer (web only)
  useEffect(() => {
    const handleLogout = () => {
      if (__DEV__) console.log('🔄 Received logout event from API layer');
      logout();
    };

    // Only add event listener on web platform
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('auth:logout', handleLogout);
      return () => window.removeEventListener('auth:logout', handleLogout);
    }

    // No cleanup needed for React Native
    return undefined;
  }, [logout]);

  return {
    ...authState,
    login,
    logout,
    hasPermission,
    getAllUsers,
    refreshProfile,
    refreshAuth: loadAuthState,
  };
}
