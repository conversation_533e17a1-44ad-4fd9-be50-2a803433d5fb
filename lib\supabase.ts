import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';
import 'react-native-url-polyfill/auto';

const supabaseUrl = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    flowType: 'pkce',
    storage: {
      getItem: (key) => {
        try {
          return localStorage.getItem(key);
        } catch (error) {
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          localStorage.setItem(key, value);
        } catch (error) {
          // Handle error
        }
      },
      removeItem: (key) => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          // Handle error
        }
      },
    },
  },
});

// Database types (will be auto-generated later)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          username: string;
          full_name: string;
          role: 'admin' | 'user';
          is_active: boolean;
          created_at: string;
          last_login: string | null;
          permissions: Record<string, boolean>;
        };
        Insert: {
          id: string;
          username: string;
          full_name: string;
          role?: 'admin' | 'user';
          is_active?: boolean;
          created_at?: string;
          last_login?: string | null;
          permissions?: Record<string, boolean>;
        };
        Update: {
          id?: string;
          username?: string;
          full_name?: string;
          role?: 'admin' | 'user';
          is_active?: boolean;
          created_at?: string;
          last_login?: string | null;
          permissions?: Record<string, boolean>;
        };
      };
      products: {
        Row: {
          id: string;
          name: string;
          stock: number;
          description: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          stock?: number;
          description?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          stock?: number;
          description?: string | null;
          created_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          product_id: string | null;
          product_name: string;
          customer_name: string;
          quantity: number;
          order_date: string;
          status: 'pending' | 'completed' | 'cancelled';
          sale_price: number | null;
        };
        Insert: {
          id?: string;
          product_id?: string | null;
          product_name: string;
          customer_name: string;
          quantity: number;
          order_date?: string;
          status?: 'pending' | 'completed' | 'cancelled';
          sale_price?: number | null;
        };
        Update: {
          id?: string;
          product_id?: string | null;
          product_name?: string;
          customer_name?: string;
          quantity?: number;
          order_date?: string;
          status?: 'pending' | 'completed' | 'cancelled';
          sale_price?: number | null;
        };
      };
      costs: {
        Row: {
          id: string;
          product_name: string;
          quantity: number;
          essence_price: number;
          essence_amount: number;
          essence_used: number;
          alcohol_price: number;
          alcohol_used: number;
          bottle_price: number;
          unit_cost: number;
          total_cost: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          product_name: string;
          quantity: number;
          essence_price: number;
          essence_amount: number;
          essence_used: number;
          alcohol_price: number;
          alcohol_used: number;
          bottle_price: number;
          unit_cost: number;
          total_cost: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          product_name?: string;
          quantity?: number;
          essence_price?: number;
          essence_amount?: number;
          essence_used?: number;
          alcohol_price?: number;
          alcohol_used?: number;
          bottle_price?: number;
          unit_cost?: number;
          total_cost?: number;
          created_at?: string;
        };
      };
    };
  };
}

// Helper functions
export const getSupabaseErrorMessage = (error: any): string => {
  if (error?.message) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unexpected error occurred';
};

export const isSupabaseError = (error: any): boolean => {
  return error && (error.message || error.code);
};
