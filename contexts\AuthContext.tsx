import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthState, User } from '@/types/User';

interface AuthContextType extends AuthState {
  setAuthState: React.Dispatch<React.SetStateAction<AuthState>>;
  forceUpdate: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });
  const [updateCounter, setUpdateCounter] = useState(0);

  const forceUpdate = () => {
    console.log('🔄 AuthContext force update triggered');
    setUpdateCounter(prev => prev + 1);
  };

  // Debug logging
  useEffect(() => {
    console.log('🔄 AuthContext state changed:', authState);
  }, [authState]);

  const value: AuthContextType = {
    ...authState,
    setAuthState,
    forceUpdate,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}
