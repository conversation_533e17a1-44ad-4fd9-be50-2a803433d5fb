import { supabase } from '@/lib/supabase';
import { AuthState, LoginCredentials, User } from '@/types/User';
import { useCallback, useState } from 'react';

export const useSupabaseAuthSimple = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false, // Start with false to prevent infinite loops
    error: null,
  });

  // Simple login function without complex state management
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      console.log('🔄 [DEBUG] Login attempt started for:', credentials.username);

      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simple email mapping for demo
      const emailMap: Record<string, string> = {
        'Gencer': '<EMAIL>',
        'Kurt': '<EMAIL>'
      };

      const email = emailMap[credentials.username];
      console.log('🔄 [DEBUG] Email mapping:', { username: credentials.username, email });

      if (!email) {
        console.log('❌ [DEBUG] No email found for username:', credentials.username);
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Invalid username or password',
        }));
        return false;
      }

      console.log('🔄 [DEBUG] Attempting Supabase auth with:', { email, password: '***' });

      // Try to sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password: credentials.password,
      });

      console.log('🔄 [DEBUG] Supabase auth response:', {
        hasData: !!data,
        hasUser: !!data?.user,
        hasError: !!error,
        errorMessage: error?.message
      });

      if (error) {
        console.error('❌ [DEBUG] Supabase auth error:', error);
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: `Auth error: ${error.message}`,
        }));
        return false;
      }

      if (data.user) {
        console.log('✅ [DEBUG] User authenticated successfully:', data.user.id);

        // Create a simple user object
        const user: User = {
          id: data.user.id,
          username: credentials.username,
          email: data.user.email || '',
          fullName: credentials.username === 'Gencer' ? 'Ana Yönetici - Gencer' : 'İkinci Yönetici - Kurt',
          role: 'admin',
          isActive: true,
          createdAt: new Date(),
          lastLogin: new Date(),
          permissions: {
            canManageProducts: true,
            canManageOrders: true,
            canManageCosts: true,
            canViewReports: true,
            canManageUsers: credentials.username === 'Gencer',
            canAddProducts: true,
            canEditProducts: true,
            canDeleteProducts: true,
            canCreateOrders: true,
            canUpdateOrderStatus: true,
            canDeleteOrders: true,
            canAddCosts: true,
            canEditCosts: true,
            canDeleteCosts: true,
            canViewFinancials: true,
            canExportReports: true,
            canViewUserActivity: credentials.username === 'Gencer',
            canChangeUserRoles: credentials.username === 'Gencer',
          },
        };

        console.log('✅ [DEBUG] Setting auth state with user:', user.username);

        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        console.log('✅ [DEBUG] Auth state updated successfully');
        return true;
      }

      console.log('❌ [DEBUG] No user in response');
      return false;
    } catch (error) {
      console.error('❌ [DEBUG] Login exception:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Login failed. Please try again.',
      }));
      return false;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      if (__DEV__) console.log('🔄 Simple logout...');
      
      await supabase.auth.signOut();
      
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });

      if (__DEV__) console.log('✅ Simple logout successful');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, []);

  return {
    ...authState,
    login,
    logout,
  };
};
