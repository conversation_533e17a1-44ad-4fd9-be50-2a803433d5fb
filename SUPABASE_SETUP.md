# Supabase Setup Guide

This guide will help you set up Supabase for the Inventory Management Application.

## 🚀 Quick Setup Steps

### 1. Create Supabase Project

1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `inventory-management`
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your location
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

### 2. Get API Keys

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon/public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (for user creation)

### 3. Update Environment Variables

1. Open `.env` file in your project root
2. Replace the placeholder values:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### 4. Create Database Tables

1. In Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `scripts/supabase-setup.sql`
3. Paste it in the SQL Editor
4. Click "Run" to execute the script

This will create:
- `products` table
- `orders` table  
- `costs` table
- `notes` table
- Row Level Security (RLS) policies
- Indexes for performance

### 5. Create Users

#### Option A: Using the Script (Recommended)

1. Install dependencies: `npm install`
2. Run the user creation script:
   ```bash
   node scripts/create-supabase-users.js
   ```
3. Enter your Supabase URL and service_role key when prompted
4. The script will create two admin users:
   - `<EMAIL>` / `Gencer103`
   - `<EMAIL>` / `Kurt123`

#### Option B: Manual Creation

1. In Supabase dashboard, go to **Authentication** > **Users**
2. Click "Add user"
3. Create first user:
   - **Email**: `<EMAIL>`
   - **Password**: `Gencer103`
   - **Email Confirm**: ✅ (checked)
   - **User Metadata**:
     ```json
     {
       "username": "Gencer",
       "full_name": "Ana Yönetici - Gencer",
       "role": "admin"
     }
     ```
4. Create second user:
   - **Email**: `<EMAIL>`
   - **Password**: `Kurt123`
   - **Email Confirm**: ✅ (checked)
   - **User Metadata**:
     ```json
     {
       "username": "Kurt", 
       "full_name": "İkinci Yönetici - Kurt",
       "role": "admin"
     }
     ```

### 6. Test the Application

1. Start the application: `npm start`
2. Open in browser: `http://localhost:8081`
3. Try logging in with:
   - **Username**: `Gencer` **Password**: `Gencer103`
   - **Username**: `Kurt` **Password**: `Kurt123`

## 🔧 Features

### Authentication
- ✅ Pure Supabase authentication (no local fallback)
- ✅ Email/password login
- ✅ Session persistence
- ✅ User metadata support

### Data Management
- ✅ Row Level Security (RLS) - users only see their own data
- ✅ Real-time updates
- ✅ User-specific data isolation
- ✅ Automatic timestamps

### Tables Structure

#### Products
- `id` (UUID, Primary Key)
- `name` (Text, NOT NULL)
- `stock` (Integer, NOT NULL)
- `description` (Text)
- `user_id` (UUID, Foreign Key)
- `created_at`, `updated_at` (Timestamps)

#### Orders
- `id` (UUID, Primary Key)
- `product_name` (Text, NOT NULL)
- `quantity` (Integer, NOT NULL)
- `status` (Text: pending/completed/cancelled)
- `sale_price` (Numeric)
- `user_id` (UUID, Foreign Key)
- `created_at`, `updated_at` (Timestamps)

#### Costs
- `id` (UUID, Primary Key)
- `product_name` (Text, NOT NULL)
- `essence_name`, `essence_amount`, `essence_price` (Numeric)
- `alcohol_amount`, `alcohol_price` (Numeric)
- `bottle_price` (Numeric)
- `user_id` (UUID, Foreign Key)
- `created_at`, `updated_at` (Timestamps)

## 🛡️ Security

- **Row Level Security**: Users can only access their own data
- **Authentication Required**: All operations require valid user session
- **API Key Protection**: anon key is safe for client-side use
- **Service Role**: Only used for admin operations (user creation)

## 🔍 Troubleshooting

### Login Issues
- Check if users exist in Authentication > Users
- Verify email/password combination
- Check browser console for error messages

### Data Issues
- Verify RLS policies are enabled
- Check if user is authenticated
- Ensure user_id is properly set in data

### Connection Issues
- Verify SUPABASE_URL and SUPABASE_ANON_KEY in .env
- Check if project is active in Supabase dashboard
- Test connection in browser network tab

## 📞 Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify Supabase project is active
3. Ensure all environment variables are correct
4. Test with a fresh browser session
