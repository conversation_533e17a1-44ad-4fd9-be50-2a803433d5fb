import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

interface CircularProgressProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  title: string;
  value: string;
  subtitle?: string;
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

export function CircularProgress({
  percentage,
  size = 120,
  strokeWidth = 8,
  color = '#34C759',
  backgroundColor = 'rgba(0, 0, 0, 0.1)',
  title,
  value,
  subtitle,
}: CircularProgressProps) {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: percentage,
      duration: 1500,
      useNativeDriver: false,
    }).start();
  }, [percentage, animatedValue]);

  const strokeDashoffset = animatedValue.interpolate({
    inputRange: [0, 100],
    outputRange: [circumference, 0],
  });

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>{title}</ThemedText>
      
      <ThemedView style={styles.progressContainer}>
        <Svg width={size} height={size} style={styles.svg}>
          {/* Background Circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundColor}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          
          {/* Progress Circle */}
          <AnimatedCircle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
          />
        </Svg>
        
        {/* Center Content */}
        <ThemedView style={[styles.centerContent, { width: size, height: size }]}>
          <ThemedText style={[styles.value, { color }]}>{value}</ThemedText>
          <ThemedText style={styles.percentage}>{percentage.toFixed(0)}%</ThemedText>
        </ThemedView>
      </ThemedView>
      
      {subtitle && (
        <ThemedText style={styles.subtitle}>{subtitle}</ThemedText>
      )}
    </ThemedView>
  );
}

// KPI Dashboard Component
interface KPIData {
  title: string;
  value: string;
  percentage: number;
  color: string;
  subtitle?: string;
}

interface KPIDashboardProps {
  kpis: KPIData[];
}

export function KPIDashboard({ kpis }: KPIDashboardProps) {
  return (
    <ThemedView style={styles.dashboardContainer}>
      <ThemedText type="subtitle" style={styles.dashboardTitle}>
        🎯 Performans Göstergeleri
      </ThemedText>
      
      <ThemedView style={styles.kpiGrid}>
        {kpis.map((kpi, index) => (
          <CircularProgress
            key={index}
            title={kpi.title}
            value={kpi.value}
            percentage={kpi.percentage}
            color={kpi.color}
            subtitle={kpi.subtitle}
            size={100}
            strokeWidth={6}
          />
        ))}
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  progressContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    transform: [{ rotate: '0deg' }],
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -60, // Adjust to center over the circle
  },
  value: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  percentage: {
    fontSize: 12,
    opacity: 0.7,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 12,
    opacity: 0.6,
    marginTop: 8,
    textAlign: 'center',
  },
  dashboardContainer: {
    marginBottom: 30,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 149, 0, 0.1)',
  },
  dashboardTitle: {
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 18,
    fontWeight: '700',
  },
  kpiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 16,
  },
});
