const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://nmaokkcclboszurxjgog.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjZGt6dmV1ZHZpanV5dndnamRvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU4MTQzOCwiZXhwIjoyMDY1MTU3NDM4fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Service role key needed for admin operations

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUsers() {
  console.log('🚀 Creating Supabase users...');

  const users = [
    {
      email: '<EMAIL>',
      password: 'Gencer103',
      username: 'Gencer',
      fullName: 'Ana Yönetici - Gencer'
    },
    {
      email: '<EMAIL>', 
      password: '<PERSON>123',
      username: '<PERSON>',
      fullName: '<PERSON><PERSON><PERSON>'
    }
  ];

  for (const user of users) {
    try {
      console.log(`\n📧 Creating user: ${user.email}`);

      // Create user with admin client
      const { data, error } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          username: user.username,
          full_name: user.fullName,
          role: 'admin'
        }
      });

      if (error) {
        console.error(`❌ Error creating ${user.email}:`, error.message);
      } else {
        console.log(`✅ Successfully created ${user.email} with ID: ${data.user.id}`);
      }

    } catch (err) {
      console.error(`💥 Exception creating ${user.email}:`, err);
    }
  }

  console.log('\n🎉 User creation process completed!');
}

// Test connection first
async function testConnection() {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    const { data, error } = await supabase.auth.admin.listUsers();
    
    if (error) {
      console.error('❌ Connection test failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful! Found', data.users.length, 'existing users');
    return true;
  } catch (err) {
    console.error('💥 Connection test exception:', err);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️ Supabase User Creation Script');
  console.log('================================');
  
  const connected = await testConnection();
  
  if (connected) {
    await createUsers();
  } else {
    console.log('❌ Cannot proceed without valid connection');
  }
}

main().catch(console.error);
