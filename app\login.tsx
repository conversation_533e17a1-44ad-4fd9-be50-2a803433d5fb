import { LoginScreen } from '@/components/LoginScreen';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/contexts/ThemeContext';
import { usePersistentAuth } from '@/hooks/usePersistentAuth';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, Platform, StatusBar, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function LoginPage() {
  const colorScheme = useColorScheme();
  const { isAuthenticated, isLoading, user } = usePersistentAuth();
  const backgroundColor = colorScheme === 'dark' ? '#000000' : '#F5F6FA';

  // Redirect to tabs if already authenticated
  React.useEffect(() => {
    if (!isLoading && isAuthenticated && user) {
      console.log('🔐 User already authenticated - redirecting to tabs');
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, isLoading, user]);

  // Show loading while checking auth state
  if (isLoading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
        <ThemedView style={[styles.container, styles.loadingContainer]}>
          <ActivityIndicator size="large" color="#007AFF" />
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
      <ThemedView style={styles.container} noBackground>
        <LoginScreen />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight || 0,
      },
    }),
  },
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
