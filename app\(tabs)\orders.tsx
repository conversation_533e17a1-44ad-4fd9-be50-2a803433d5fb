import { OrderForm } from '@/components/OrderForm';
import { OrderList } from '@/components/OrderList';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useSupabaseOrders as useOrders } from '@/hooks/useSupabaseOrders';
import { useSupabaseProducts as useProducts } from '@/hooks/useSupabaseProducts';
import { useThemeColor } from '@/hooks/useThemeColor';
import React from 'react';
import { ActivityIndicator, Platform, ScrollView, StatusBar, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function OrdersScreen() {
  const { orders, isLoading: ordersLoading, isRefreshing, addOrder, updateOrderStatus, deleteOrder, error: ordersError } = useOrders();
  const { products, isLoading: productsLoading, error: productsError } = useProducts();
  const backgroundColor = useThemeColor({}, 'background');
  const absoluteBackgroundColor = useThemeColor({ light: '#F5F6FA', dark: '#000000' }, 'background');

  const handleAddOrder = async (productName: string, customerName: string, quantity: number) => {
    try {
      // Find product by name to get productId (optional for orders not in inventory)
      const product = products.find(p => p.name.toLowerCase() === productName.toLowerCase());

      console.log('Creating order with data:', {
        productId: product?.id,
        productName: productName.trim(),
        customerName: customerName.trim(),
        quantity,
        status: 'pending'
      });

      const success = await addOrder({
        productId: product?.id, // Optional - can be undefined for products not in inventory
        productName: productName.trim(),
        customerName: customerName.trim(),
        quantity,
        status: 'pending',
        salePrice: undefined, // Will be set when order is completed
      });

      if (!success) {
        console.error('Order creation failed:', ordersError);
        throw new Error(ordersError || 'Sipariş oluşturulamadı');
      }
    } catch (error) {
      console.error('Error in handleAddOrder:', error);
      throw error; // Re-throw to let OrderForm handle the error
    }
  };

  // ONLY SHOW FULL LOADING SCREEN FOR INITIAL LOAD
  if (ordersLoading || productsLoading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <ThemedText style={styles.loadingText}>Veriler yükleniyor...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
      <ThemedView style={styles.container} noBackground>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <ThemedView style={styles.header}>
            <ThemedText type="title" style={styles.title}>
              Sipariş Yönetimi
            </ThemedText>

            {/* SMALL REFRESH INDICATOR FOR BACKGROUND UPDATES */}
            {isRefreshing && (
              <ThemedView style={styles.refreshIndicator}>
                <ActivityIndicator size="small" color="#007AFF" />
              </ThemedView>
            )}
          </ThemedView>

          <OrderForm
            products={products}
            onAddOrder={handleAddOrder}
          />

          <OrderList
            orders={orders}
            onUpdateOrderStatus={updateOrderStatus}
            onDeleteOrder={deleteOrder}
            scrollEnabled={false}
          />
        </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    // backgroundColor will be set dynamically via useThemeColor
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 0, // Remove bottom padding for Android
        marginBottom: -20, // Prevent bottom overlay
      },
    }),
  },
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80, // Android: Space for tab bar
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    textAlign: 'center',
  },
  refreshIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
  },

});
