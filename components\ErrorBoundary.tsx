import React from 'react';
import { Platform, StyleSheet, Text, View } from 'react-native';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to console in development
    if (__DEV__) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    this.setState({
      error,
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      if (__DEV__) {
        // Development mode: Show detailed error
        return (
          <View style={styles.container}>
            <Text style={styles.title}>Bir Hata Oluştu</Text>
            <Text style={styles.errorText}>{this.state.error?.toString()}</Text>
            <Text style={styles.stackTrace}>
              {this.state.errorInfo?.componentStack}
            </Text>
          </View>
        );
      }

      // Production mode: Show user-friendly message
      return (
        <View style={styles.container}>
          <Text style={styles.title}>Bir Hata Oluştu</Text>
          <Text style={styles.message}>
            Uygulamayı yeniden başlatın veya destekle iletişime geçin.
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#FF3B30',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    marginBottom: 20,
    fontFamily: Platform.select({ ios: 'Menlo', android: 'monospace' }),
  },
  stackTrace: {
    fontSize: 12,
    color: '#666',
    fontFamily: Platform.select({ ios: 'Menlo', android: 'monospace' }),
    padding: 10,
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
    width: '100%',
  },
}); 