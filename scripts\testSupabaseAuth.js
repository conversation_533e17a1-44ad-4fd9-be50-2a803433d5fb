const { createClient } = require('@supabase/supabase-js');

// Supabase configuration from .env
const supabaseUrl = 'https://nmaokkcclboszurxjgog.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjZGt6dmV1ZHZpanV5dndnamRvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1ODE0MzgsImV4cCI6MjA2NTE1NzQzOH0.lwXvPudemQweod5uo4gxEUuqvAFAOqxXHnHj4f9K6-Y';

// Create Supabase client (same as app)
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

async function testLogin() {
  console.log('🚀 Testing Supabase Authentication');
  console.log('==================================');

  const testUsers = [
    { email: '<EMAIL>', password: 'Gencer103', username: 'Gen<PERSON>' },
    { email: '<EMAIL>', password: 'Kurt123', username: '<PERSON>' }
  ];

  for (const user of testUsers) {
    try {
      console.log(`\n🔐 Testing login for: ${user.username} (${user.email})`);

      // Test signInWithPassword
      const { data, error } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: user.password,
      });

      if (error) {
        console.error(`❌ Login failed for ${user.username}:`, error.message);
        console.error('   Error details:', error);
      } else if (data.user && data.session) {
        console.log(`✅ Login successful for ${user.username}!`);
        console.log(`   User ID: ${data.user.id}`);
        console.log(`   Email: ${data.user.email}`);
        console.log(`   Session: ${data.session.access_token ? 'Valid' : 'Invalid'}`);
        
        // Sign out after successful test
        await supabase.auth.signOut();
        console.log(`🔓 Signed out ${user.username}`);
      } else {
        console.log(`❌ Login failed for ${user.username}: No user or session returned`);
      }

    } catch (err) {
      console.error(`💥 Exception testing ${user.username}:`, err);
    }
  }

  console.log('\n🎉 Authentication test completed!');
}

// Test connection first
async function testConnection() {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    // Try to get current session (should be null initially)
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Connection test failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful!');
    console.log('   Current session:', session ? 'Active' : 'None');
    return true;
  } catch (err) {
    console.error('💥 Connection test exception:', err);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  
  if (connected) {
    await testLogin();
  } else {
    console.log('❌ Cannot proceed without valid connection');
  }
}

main().catch(console.error);
