import { Order } from '@/types/Order';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

const ORDERS_STORAGE_KEY = '@inventory_orders';
const PRODUCTS_STORAGE_KEY = '@inventory_products';

export function useOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  // Load orders from storage
  useEffect(() => {
    loadOrders();

    // Set up interval to refresh orders every 2 seconds
    const interval = setInterval(() => {
      loadOrders();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const loadOrders = async () => {
    try {
      const storedOrders = await AsyncStorage.getItem(ORDERS_STORAGE_KEY);
      if (storedOrders) {
        const parsedOrders = JSON.parse(storedOrders).map((order: any) => ({
          ...order,
          orderDate: new Date(order.orderDate),
        }));
        setOrders(parsedOrders);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveOrders = async (newOrders: Order[]) => {
    try {
      await AsyncStorage.setItem(ORDERS_STORAGE_KEY, JSON.stringify(newOrders));
      setOrders(newOrders);
    } catch (error) {
      console.error('Error saving orders:', error);
    }
  };

  const addOrder = async (productName: string, customerName: string, quantity: number, productId?: string) => {
    const newOrder: Order = {
      id: Date.now().toString(),
      productId,
      productName,
      customerName,
      quantity,
      orderDate: new Date(),
      status: 'pending',
    };

    const updatedOrders = [...orders, newOrder];
    await saveOrders(updatedOrders);
  };

  const updateOrderStatus = async (orderId: string, status: Order['status'], salePrice?: number) => {
    const order = orders.find(o => o.id === orderId);

    console.log('updateOrderStatus called:', { orderId, status, salePrice, order });

    // If order is being completed, reduce stock
    if (status === 'completed' && order) {
      console.log('Reducing stock for:', order.productName, 'quantity:', order.quantity);
      await reduceProductStock(order.productName, order.quantity);
    }

    const updatedOrders = orders.map(order =>
      order.id === orderId ? { ...order, status, ...(salePrice && { salePrice }) } : order
    );
    await saveOrders(updatedOrders);
  };

  const reduceProductStock = async (productName: string, quantity: number) => {
    try {
      console.log('reduceProductStock called with:', { productName, quantity });

      // Load products from storage
      const storedProducts = await AsyncStorage.getItem(PRODUCTS_STORAGE_KEY);
      console.log('Stored products raw:', storedProducts);

      if (storedProducts) {
        const products = JSON.parse(storedProducts).map((product: any) => ({
          ...product,
          createdAt: new Date(product.createdAt),
        }));

        console.log('Parsed products:', products);
        console.log('Looking for product:', productName);

        // Find product by name (case insensitive)
        const productIndex = products.findIndex(
          (p: any) => p.name.toLowerCase() === productName.toLowerCase()
        );

        console.log('Product index found:', productIndex);

        if (productIndex !== -1) {
          const oldStock = products[productIndex].stock;
          // Reduce stock
          products[productIndex].stock = Math.max(0, products[productIndex].stock - quantity);

          console.log(`Stock change: ${oldStock} → ${products[productIndex].stock}`);

          // Save updated products
          await AsyncStorage.setItem(PRODUCTS_STORAGE_KEY, JSON.stringify(products));

          // Emit event to refresh products in other components
          if (typeof DeviceEventEmitter !== 'undefined') {
            DeviceEventEmitter.emit('productsUpdated');
          }

          console.log(`Stock reduced for ${productName}: -${quantity}, New stock: ${products[productIndex].stock}`);
        } else {
          console.log(`Product not found in stock: ${productName}`);
          console.log('Available products:', products.map(p => p.name));
        }
      } else {
        console.log('No products found in storage');
      }
    } catch (error) {
      console.error('Error reducing product stock:', error);
    }
  };

  const deleteOrder = async (orderId: string) => {
    const updatedOrders = orders.filter(order => order.id !== orderId);
    await saveOrders(updatedOrders);
  };

  return {
    orders,
    loading,
    addOrder,
    updateOrderStatus,
    deleteOrder,
    refreshOrders: loadOrders,
  };
}
