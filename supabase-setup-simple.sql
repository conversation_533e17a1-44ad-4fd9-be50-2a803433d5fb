-- Simplified Supabase Database Setup Script
-- Bu script'i Supabase SQL Editor'da çalıştırın

-- 1. Drop existing tables if they exist (to start fresh)
DROP TABLE IF EXISTS public.costs CASCADE;
DROP TABLE IF EXISTS public.orders CASCADE;
DROP TABLE IF EXISTS public.products CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- 2. Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_login TIMESTAMPTZ,
  permissions JSONB DEFAULT '{}'::jsonb
);

-- 3. Products table
CREATE TABLE public.products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  stock INTEGER DEFAULT 0,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Orders table
CREATE TABLE public.orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID,
  product_name TEXT NOT NULL,
  customer_name TEXT NOT NULL,
  quantity INTEGER NOT NULL,
  order_date TIMESTAMPTZ DEFAULT NOW(),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
  sale_price DECIMAL(10,2)
);

-- 5. Costs table
CREATE TABLE public.costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_name TEXT NOT NULL,
  quantity INTEGER NOT NULL,
  essence_price DECIMAL(10,2) NOT NULL,
  essence_amount DECIMAL(10,2) NOT NULL,
  essence_used DECIMAL(10,2) NOT NULL,
  alcohol_price DECIMAL(10,2) NOT NULL,
  alcohol_used DECIMAL(10,2) NOT NULL,
  bottle_price DECIMAL(10,2) NOT NULL,
  unit_cost DECIMAL(10,2) NOT NULL,
  total_cost DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Add foreign key constraint for orders (separate to avoid circular dependency)
ALTER TABLE public.orders 
ADD CONSTRAINT fk_orders_product_id 
FOREIGN KEY (product_id) REFERENCES public.products(id) ON DELETE SET NULL;

-- 7. Function to create user profile after auth user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, username, full_name, role, permissions)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
    COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
    COALESCE(NEW.raw_user_meta_data->>'permissions', '{}')::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Trigger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 9. Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.costs ENABLE ROW LEVEL SECURITY;

-- 10. Create simple RLS policies
-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

-- Admins can read all users
CREATE POLICY "Admins can read all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Everyone can read products
CREATE POLICY "Everyone can read products" ON public.products
  FOR SELECT USING (true);

-- Authenticated users can manage products
CREATE POLICY "Authenticated users can manage products" ON public.products
  FOR ALL USING (auth.role() = 'authenticated');

-- Everyone can read orders
CREATE POLICY "Everyone can read orders" ON public.orders
  FOR SELECT USING (true);

-- Authenticated users can manage orders
CREATE POLICY "Authenticated users can manage orders" ON public.orders
  FOR ALL USING (auth.role() = 'authenticated');

-- Everyone can read costs
CREATE POLICY "Everyone can read costs" ON public.costs
  FOR SELECT USING (true);

-- Authenticated users can manage costs
CREATE POLICY "Authenticated users can manage costs" ON public.costs
  FOR ALL USING (auth.role() = 'authenticated');

-- 11. Create indexes for better performance
CREATE INDEX idx_users_username ON public.users(username);
CREATE INDEX idx_products_name ON public.products(name);
CREATE INDEX idx_orders_status ON public.orders(status);
CREATE INDEX idx_orders_date ON public.orders(order_date);
CREATE INDEX idx_costs_product_name ON public.costs(product_name);
CREATE INDEX idx_costs_date ON public.costs(created_at);

-- 12. Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Script completed successfully!
-- Now create auth users in Supabase Dashboard:
-- 1. Go to Authentication > Users
-- 2. Add user: <EMAIL> / Gencer103
-- 3. Add user: <EMAIL> / Kurt123
