// Global Splash State Store
// Bu store splash screen'in sadece bir kere gösterilmesini sağlar

// Global state - component re-render'larda<PERSON> bağımsız
let globalIsSplashFinished = false;

// State change listeners
const listeners: (() => void)[] = [];

// Global splash state management
export const splashStore = {
  // Current state getter
  getIsSplashFinished: (): boolean => {
    return globalIsSplashFinished;
  },

  // Set splash as finished
  setSplashFinished: (finished: boolean): void => {
    if (__DEV__) console.log(`🎬 Setting splash finished: ${finished}`);
    globalIsSplashFinished = finished;

    // Notify all listeners
    listeners.forEach(listener => listener());
  },

  // Subscribe to state changes
  subscribe: (listener: () => void): (() => void) => {
    listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  },

  // Reset splash state (for testing)
  reset: (): void => {
    console.log('🎬 Resetting splash state');
    globalIsSplashFinished = false;
    listeners.forEach(listener => listener());
  },

  // Debug info
  getDebugInfo: () => {
    return {
      isSplashFinished: globalIsSplashFinished,
      listenersCount: listeners.length,
      timestamp: new Date().toISOString()
    };
  }
};

// Debug function for browser console
if (typeof window !== 'undefined') {
  (window as any).splashStore = splashStore;
  (window as any).resetSplash = () => {
    splashStore.reset();
    console.log('🎬 Splash state reset - reload to see splash again');
  };
}
