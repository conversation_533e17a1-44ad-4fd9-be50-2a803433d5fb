{"name": "veldae", "main": "expo-router/entry", "version": "1.3.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/datetimepicker": "7.2.0", "@react-native-community/netinfo": "9.3.10", "@react-native-picker/picker": "2.4.10", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/elements": "^1.3.30", "@react-navigation/native": "^6.1.17", "@supabase/supabase-js": "^2.50.0", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "expo": "~52.0.0", "expo-blur": "~12.4.1", "expo-build-properties": "~0.8.3", "expo-constants": "~14.4.2", "expo-dev-client": "~2.4.12", "expo-font": "~11.4.0", "expo-haptics": "~12.4.0", "expo-image": "~1.10.5", "expo-linking": "~5.0.2", "expo-navigation-bar": "~2.4.1", "expo-router": "~3.5.23", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-symbols": "~0.1.0", "expo-system-ui": "~2.4.0", "expo-updates": "~0.18.17", "expo-web-browser": "~12.3.2", "lodash": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.31.1", "react-native-svg": "14.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.19.6", "react-native-webview": "13.6.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.17.17", "@types/react": "~18.2.45", "eslint": "^8.56.0", "typescript": "5.1.6"}, "overrides": {"eslint-config-expo": "6.0.0"}, "private": true}