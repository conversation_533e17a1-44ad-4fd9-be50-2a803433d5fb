// Script to create Supabase users
// Run with: node scripts/create-supabase-users.js

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
const prompt = (question) => new Promise((resolve) => {
  rl.question(question, (answer) => resolve(answer));
});

async function main() {
  console.log('🚀 Supabase User Creation Script');
  console.log('================================');
  
  // Get Supabase credentials from user input
  const supabaseUrl = await prompt('Enter your Supabase URL: ');
  const supabaseServiceKey = await prompt('Enter your Supabase service_role key: ');
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Supabase URL and service_role key are required');
    rl.close();
    return;
  }
  
  // Create Supabase admin client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  
  console.log('\n🔍 Testing Supabase connection...');
  
  try {
    // Test connection
    const { data, error } = await supabase.auth.admin.listUsers();
    
    if (error) {
      console.error('❌ Connection test failed:', error.message);
      rl.close();
      return;
    }
    
    console.log('✅ Connection successful!');
    console.log(`   Found ${data.users.length} existing users`);
    
    // Create the required users
    await createUsers(supabase);
    
  } catch (err) {
    console.error('💥 Connection test exception:', err);
  }
  
  rl.close();
}

async function createUsers(supabase) {
  console.log('\n🚀 Creating Supabase users...');

  const users = [
    {
      email: '<EMAIL>',
      password: 'Gencer103',
      username: 'Gencer',
      fullName: 'Ana Yönetici - Gencer'
    },
    {
      email: '<EMAIL>', 
      password: 'Kurt123',
      username: 'Kurt',
      fullName: 'İkinci Yönetici - Kurt'
    }
  ];

  for (const user of users) {
    try {
      console.log(`\n📧 Creating user: ${user.email}`);

      // Create user with admin client
      const { data, error } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          username: user.username,
          full_name: user.fullName,
          role: 'admin'
        }
      });

      if (error) {
        console.error(`❌ Error creating ${user.email}:`, error.message);
      } else {
        console.log(`✅ Successfully created ${user.email} with ID: ${data.user.id}`);
      }

    } catch (err) {
      console.error(`💥 Exception creating ${user.email}:`, err);
    }
  }

  console.log('\n🎉 User creation process completed!');
}

main().catch(console.error);
