import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import React, { useEffect } from 'react';
import { Animated, StyleSheet, Text } from 'react-native';

export function NetworkStatusBanner() {
  const { isOffline } = useNetworkStatus();
  const translateY = React.useRef(new Animated.Value(-50)).current;

  useEffect(() => {
    Animated.spring(translateY, {
      toValue: isOffline ? 0 : -50,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start();
  }, [isOffline]);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY }]
        }
      ]}
    >
      <Text style={styles.text}>İnternet bağlantısı yok</Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FF3B30',
    padding: 8,
    zIndex: 1000,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
}); 