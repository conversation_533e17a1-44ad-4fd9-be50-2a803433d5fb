// Geçici script - Supabase'de kullanıcıları oluşturmak için
// Bu script'i bir kez çalı<PERSON>ı<PERSON>n, sonra silin

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://nmaokkcclboszurxjgog.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjZGt6dmV1ZHZpanV5dndnamRvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU4MTQzOCwiZXhwIjoyMDY1MTU3NDM4fQ.YOUR_SERVICE_ROLE_KEY'; // Service role key gerekli

// NOT: Service role key'iniz yoksa manuel olarak oluşturun

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUsers() {
  console.log('🔄 Creating Supabase users...');

  try {
    // Gencer kullanıcısını oluştur
    const { data: gencerData, error: gencerError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Gencer103',
      email_confirm: true,
      user_metadata: {
        username: 'Gencer',
        full_name: 'Ana Yönetici - Gencer',
        role: 'admin'
      }
    });

    if (gencerError) {
      console.error('❌ Gencer user creation error:', gencerError);
    } else {
      console.log('✅ Gencer user created:', gencerData.user.email);
    }

    // Kurt kullanıcısını oluştur
    const { data: kurtData, error: kurtError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Kurt123',
      email_confirm: true,
      user_metadata: {
        username: 'Kurt',
        full_name: 'İkinci Yönetici - Kurt',
        role: 'admin'
      }
    });

    if (kurtError) {
      console.error('❌ Kurt user creation error:', kurtError);
    } else {
      console.log('✅ Kurt user created:', kurtData.user.email);
    }

    console.log('🎉 User creation completed!');
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

createUsers();
