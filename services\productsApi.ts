/**
 * Products API Service
 * Handles product CRUD operations with backend synchronization
 */

import { Product } from '@/types/Product';
import { apiClient, ApiError } from './api';

export interface ProductsResponse {
  success: boolean;
  products: Product[];
  total?: number;
  page?: number;
  limit?: number;
}

export interface ProductResponse {
  success: boolean;
  product: Product;
  message?: string;
}

export interface DeleteProductResponse {
  success: boolean;
  message: string;
}

export interface CreateProductRequest {
  name: string;
  stock: number;
  description?: string;
}

export interface UpdateProductRequest {
  id: string;
  name?: string;
  stock?: number;
  description?: string;
}

export interface UpdateStockRequest {
  id: string;
  stock: number;
  operation?: 'set' | 'add' | 'subtract';
}

// Products API endpoints
const PRODUCTS_ENDPOINTS = {
  LIST: '/products',
  CREATE: '/products',
  GET: (id: string) => `/products/${id}`,
  UPDATE: (id: string) => `/products/${id}`,
  DELETE: (id: string) => `/products/${id}`,
  UPDATE_STOCK: (id: string) => `/products/${id}/stock`,
  SEARCH: '/products/search',
} as const;

/**
 * Products API Service Class
 */
export class ProductsApiService {
  /**
   * Get all products
   */
  async getProducts(page = 1, limit = 100): Promise<Product[]> {
    try {
      console.log('📦 API: Fetching products');

      const response = await apiClient.get<ProductsResponse>(
        `${PRODUCTS_ENDPOINTS.LIST}?page=${page}&limit=${limit}`
      );

      if (response.success && response.products) {
        console.log(`✅ API: Fetched ${response.products.length} products`);
        
        // Convert date strings to Date objects
        const products = response.products.map(product => ({
          ...product,
          createdAt: product.createdAt ? new Date(product.createdAt) : new Date(),
        }));
        
        return products;
      } else {
        throw new ApiError('Failed to fetch products');
      }
    } catch (error) {
      console.error('❌ API: Products fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch products');
    }
  }

  /**
   * Get single product by ID
   */
  async getProduct(id: string): Promise<Product> {
    try {
      console.log('📦 API: Fetching product:', id);

      const response = await apiClient.get<ProductResponse>(PRODUCTS_ENDPOINTS.GET(id));

      if (response.success && response.product) {
        console.log('✅ API: Product fetched successfully');
        
        // Convert date string to Date object
        const product = {
          ...response.product,
          createdAt: response.product.createdAt ? new Date(response.product.createdAt) : new Date(),
        };
        
        return product;
      } else {
        throw new ApiError('Product not found');
      }
    } catch (error) {
      console.error('❌ API: Product fetch error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to fetch product');
    }
  }

  /**
   * Create new product
   */
  async createProduct(productData: CreateProductRequest): Promise<Product> {
    try {
      console.log('📦 API: Creating product:', productData.name);

      const response = await apiClient.post<ProductResponse>(PRODUCTS_ENDPOINTS.CREATE, {
        name: productData.name.trim(),
        stock: productData.stock,
        description: productData.description?.trim() || '',
      });

      if (response.success && response.product) {
        console.log('✅ API: Product created successfully');
        
        // Convert date string to Date object
        const product = {
          ...response.product,
          createdAt: response.product.createdAt ? new Date(response.product.createdAt) : new Date(),
        };
        
        return product;
      } else {
        throw new ApiError(response.message || 'Failed to create product');
      }
    } catch (error) {
      console.error('❌ API: Product creation error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to create product');
    }
  }

  /**
   * Update existing product
   */
  async updateProduct(productData: UpdateProductRequest): Promise<Product> {
    try {
      console.log('📦 API: Updating product:', productData.id);

      const updateData: any = {};
      if (productData.name !== undefined) updateData.name = productData.name.trim();
      if (productData.stock !== undefined) updateData.stock = productData.stock;
      if (productData.description !== undefined) updateData.description = productData.description.trim();

      const response = await apiClient.put<ProductResponse>(
        PRODUCTS_ENDPOINTS.UPDATE(productData.id),
        updateData
      );

      if (response.success && response.product) {
        console.log('✅ API: Product updated successfully');
        
        // Convert date string to Date object
        const product = {
          ...response.product,
          createdAt: response.product.createdAt ? new Date(response.product.createdAt) : new Date(),
        };
        
        return product;
      } else {
        throw new ApiError(response.message || 'Failed to update product');
      }
    } catch (error) {
      console.error('❌ API: Product update error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to update product');
    }
  }

  /**
   * Update product stock
   */
  async updateProductStock(stockData: UpdateStockRequest): Promise<Product> {
    try {
      console.log('📦 API: Updating product stock:', stockData.id, stockData.stock);

      const response = await apiClient.put<ProductResponse>(
        PRODUCTS_ENDPOINTS.UPDATE_STOCK(stockData.id),
        {
          stock: stockData.stock,
          operation: stockData.operation || 'set',
        }
      );

      if (response.success && response.product) {
        console.log('✅ API: Product stock updated successfully');
        
        // Convert date string to Date object
        const product = {
          ...response.product,
          createdAt: response.product.createdAt ? new Date(response.product.createdAt) : new Date(),
        };
        
        return product;
      } else {
        throw new ApiError(response.message || 'Failed to update product stock');
      }
    } catch (error) {
      console.error('❌ API: Product stock update error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to update product stock');
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(id: string): Promise<void> {
    try {
      console.log('📦 API: Deleting product:', id);

      const response = await apiClient.delete<DeleteProductResponse>(PRODUCTS_ENDPOINTS.DELETE(id));

      if (response.success) {
        console.log('✅ API: Product deleted successfully');
      } else {
        throw new ApiError(response.message || 'Failed to delete product');
      }
    } catch (error) {
      console.error('❌ API: Product deletion error:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError('Failed to delete product');
    }
  }

  /**
   * Search products by name
   */
  async searchProducts(query: string): Promise<Product[]> {
    try {
      console.log('📦 API: Searching products:', query);

      const response = await apiClient.get<ProductsResponse>(
        `${PRODUCTS_ENDPOINTS.SEARCH}?q=${encodeURIComponent(query)}`
      );

      if (response.success && response.products) {
        console.log(`✅ API: Found ${response.products.length} products`);
        
        // Convert date strings to Date objects
        const products = response.products.map(product => ({
          ...product,
          createdAt: product.createdAt ? new Date(product.createdAt) : new Date(),
        }));
        
        return products;
      } else {
        return [];
      }
    } catch (error) {
      console.error('❌ API: Product search error:', error);
      return [];
    }
  }
}

// Create and export products API service instance
export const productsApi = new ProductsApiService();

// Types are already exported above
