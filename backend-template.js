// Backend Template - Express Server
// Bu dosyayı ayrı bir backend projesinde kullanın

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// In-memory storage (production'da database kullanın)
let users = [
  {
    id: '1',
    username: 'Gencer',
    password: '$2a$10$hashedpassword', // bcrypt ile hash'leyin
    email: '<EMAIL>',
    fullName: 'Ana Yönetici',
    role: 'admin',
    isActive: true,
    createdAt: new Date(),
    lastLogin: new Date(),
    permissions: {
      canManageProducts: true,
      canManageOrders: true,
      canManageCosts: true,
      canViewReports: true,
      canManageUsers: true
    }
  },
  {
    id: '2',
    username: '<PERSON>',
    password: '$2a$10$hashedpassword', // bcrypt ile hash'leyin
    email: '<EMAIL>',
    fullName: 'İkinci Yönetici',
    role: 'admin',
    isActive: true,
    createdAt: new Date(),
    lastLogin: new Date(),
    permissions: {
      canManageProducts: true,
      canManageOrders: true,
      canManageCosts: true,
      canViewReports: true,
      canManageUsers: false
    }
  }
];

let products = [];
let orders = [];
let costs = [];

// Auth Routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // Find user
    const user = users.find(u => u.username === username);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    // Verify password (production'da bcrypt.compare kullanın)
    const validPassword = password === 'Gencer103' || password === 'Kurt123';
    if (!validPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    // Generate JWT token (production'da gerçek JWT kullanın)
    const token = 'fake-jwt-token-' + user.id;
    const refreshToken = 'fake-refresh-token-' + user.id;
    
    // Update last login
    user.lastLogin = new Date();
    
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
        permissions: user.permissions
      },
      token,
      refreshToken,
      message: 'Login successful'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

// Products Routes
app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    data: products
  });
});

app.post('/api/products', (req, res) => {
  const product = {
    id: Date.now().toString(),
    ...req.body,
    createdAt: new Date()
  };
  products.push(product);
  
  res.json({
    success: true,
    data: product,
    message: 'Product created successfully'
  });
});

app.put('/api/products/:id', (req, res) => {
  const { id } = req.params;
  const index = products.findIndex(p => p.id === id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }
  
  products[index] = { ...products[index], ...req.body };
  
  res.json({
    success: true,
    data: products[index],
    message: 'Product updated successfully'
  });
});

app.delete('/api/products/:id', (req, res) => {
  const { id } = req.params;
  const index = products.findIndex(p => p.id === id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'Product not found'
    });
  }
  
  products.splice(index, 1);
  
  res.json({
    success: true,
    message: 'Product deleted successfully'
  });
});

// Orders Routes
app.get('/api/orders', (req, res) => {
  res.json({
    success: true,
    data: orders
  });
});

app.post('/api/orders', (req, res) => {
  const order = {
    id: Date.now().toString(),
    ...req.body,
    orderDate: new Date()
  };
  orders.push(order);
  
  res.json({
    success: true,
    data: order,
    message: 'Order created successfully'
  });
});

// Costs Routes
app.get('/api/costs', (req, res) => {
  res.json({
    success: true,
    data: costs
  });
});

app.post('/api/costs', (req, res) => {
  const cost = {
    id: Date.now().toString(),
    ...req.body,
    createdAt: new Date()
  };
  costs.push(cost);
  
  res.json({
    success: true,
    data: cost,
    message: 'Cost created successfully'
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend server running on http://localhost:${PORT}`);
  console.log(`📊 API endpoints available at http://localhost:${PORT}/api`);
  console.log(`🔐 Test login: Gencer/Gencer103 or Kurt/Kurt123`);
});

module.exports = app;
