import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Cost } from '@/types/Cost';
import { Alert, FlatList, StyleSheet, TouchableOpacity } from 'react-native';

interface CostListProps {
  costs: Cost[];
  onDeleteCost: (costId: string) => Promise<void>;
}

export function CostList({ costs, onDeleteCost }: CostListProps) {
  const borderColor = useThemeColor({ light: '#eee', dark: '#333' }, 'text');
  const iconColor = useThemeColor({ light: '#ff4444', dark: '#ff6666' }, 'text');

  const handleDeleteCost = (cost: Cost) => {
    Alert.alert(
      'Maliyet Kaydını Sil',
      `"${cost.productName}" maliyet kaydını silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive', 
          onPress: () => onDeleteCost(cost.id) 
        },
      ]
    );
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const renderCost = ({ item }: { item: Cost }) => (
    <ThemedView style={[styles.costItem, { borderColor }]}>
      <ThemedView style={styles.costHeader}>
        <ThemedView style={styles.costInfo}>
          <ThemedText type="defaultSemiBold" style={styles.productName}>
            {item.productName}
          </ThemedText>
          <ThemedText style={styles.quantityInfo}>
            {item.quantity} adet × {(item.unitCost || item.unit_cost || 0).toFixed(2)}₺
          </ThemedText>
          <ThemedText style={styles.date}>
            {formatDate(item.createdAt || new Date(item.created_at))}
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.totalCostContainer}>
          <ThemedText style={styles.totalCostLabel}>Toplam</ThemedText>
          <ThemedText style={styles.totalCost}>
            {(item.totalCost || item.total_cost || 0).toFixed(2)} ₺
          </ThemedText>
        </ThemedView>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteCost(item)}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <IconSymbol
            name="trash"
            size={20}
            color={iconColor}
          />
        </TouchableOpacity>
      </ThemedView>
      
      <ThemedView style={styles.costDetails}>
        <ThemedView style={styles.detailRow}>
          <ThemedText style={styles.detailLabel}>Esans:</ThemedText>
          <ThemedText style={styles.detailValue}>
            {item.essence_used}ml × {((item.essencePrice || item.essence_price || 0) / (item.essenceAmount || item.essence_amount || 1)).toFixed(2)}₺ = {(((item.essencePrice || item.essence_price || 0) / (item.essenceAmount || item.essence_amount || 1)) * item.essence_used).toFixed(2)}₺
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.detailRow}>
          <ThemedText style={styles.detailLabel}>Alkol:</ThemedText>
          <ThemedText style={styles.detailValue}>
            {item.alcohol_used}ml × {((item.alcoholPrice || item.alcohol_price || 0) / 1000).toFixed(2)}₺ = {(((item.alcoholPrice || item.alcohol_price || 0) / 1000) * item.alcohol_used).toFixed(2)}₺
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.detailRow}>
          <ThemedText style={styles.detailLabel}>Şişe:</ThemedText>
          <ThemedText style={styles.detailValue}>
            {(item.bottlePrice || item.bottle_price || 0).toFixed(2)}₺
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );

  const renderEmptyComponent = () => (
    <ThemedView style={styles.emptyContainer}>
      <ThemedText style={styles.emptyText}>
        Henüz maliyet kaydı bulunmuyor
      </ThemedText>
      <ThemedText style={styles.emptySubtext}>
        Yukarıdaki formu kullanarak ilk maliyet kaydınızı ekleyin
      </ThemedText>
    </ThemedView>
  );

  const renderHeader = () => (
    <ThemedText type="subtitle" style={styles.title}>
      Maliyet Geçmişi ({costs.length})
    </ThemedText>
  );

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={costs.sort((a, b) => {
          const aDate = a.createdAt || new Date(a.created_at);
          const bDate = b.createdAt || new Date(b.created_at);
          return bDate.getTime() - aDate.getTime();
        })}
        renderItem={renderCost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={costs.length === 0 ? styles.emptyList : styles.listContent}
        style={styles.flatList} // ENSURE FULL HEIGHT
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    marginBottom: -30, // Android: Prevent bottom overlay
  },
  flatList: {
    flex: 1, // ENSURE FLATLIST TAKES FULL HEIGHT
  },
  title: {
    marginBottom: 15,
    textAlign: 'center',
  },
  costItem: {
    borderWidth: 1,
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  costHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  costInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    marginBottom: 2,
  },
  quantityInfo: {
    fontSize: 13,
    color: '#34c759',
    fontWeight: '600',
    marginBottom: 2,
  },
  date: {
    fontSize: 12,
    opacity: 0.6,
  },
  totalCostContainer: {
    alignItems: 'center',
    marginRight: 10,
  },
  totalCostLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  totalCost: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  deleteButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(231, 111, 81, 0.1)',
  },
  costDetails: {
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    minWidth: 60,
  },
  detailValue: {
    fontSize: 14,
    opacity: 0.8,
    flex: 1,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    minHeight: 200, // MINIMUM HEIGHT FOR EMPTY STATE
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  emptyList: {
    flexGrow: 1, // GROW TO FILL AVAILABLE SPACE
    justifyContent: 'center', // CENTER EMPTY CONTENT
  },
  listContent: {
    paddingBottom: 0, // Android: Remove bottom padding
    flexGrow: 1,
  },
});
