/**
 * New Costs Hook with Backend Integration
 * Handles cost CRUD operations with backend synchronization
 */

import { Cost, CostSummary } from '@/types/Cost';
import { costsApi, CreateCostRequest } from '@/services/costsApi';
import { ApiError } from '@/services/api';
import { useCallback, useEffect, useState } from 'react';

// Global costs state for real-time sync
let globalCosts: Cost[] = [];
let globalCostsListeners: Set<(costs: Cost[]) => void> = new Set();

// Global costs state management
const setGlobalCosts = (costs: Cost[]) => {
  globalCosts = costs;
  globalCostsListeners.forEach(listener => {
    try {
      listener(costs);
    } catch (error) {
      if (__DEV__) console.error('Costs listener error:', error);
    }
  });
};

const addGlobalCostsListener = (listener: (costs: Cost[]) => void) => {
  globalCostsListeners.add(listener);
  return () => globalCostsListeners.delete(listener);
};

/**
 * Enhanced Costs Hook with Backend Integration
 */
export function useCostsNew() {
  const [costs, setCosts] = useState<Cost[]>(globalCosts);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load costs from backend
   */
  const loadCosts = useCallback(async () => {
    try {
      if (__DEV__) console.log('💰 Loading costs from backend...');
      setLoading(true);
      setError(null);

      const fetchedCosts = await costsApi.getCosts();
      
      // Sort costs by creation date (newest first)
      const sortedCosts = fetchedCosts.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setGlobalCosts(sortedCosts);
      setCosts(sortedCosts);

      if (__DEV__) console.log(`✅ Loaded ${fetchedCosts.length} costs from backend`);
    } catch (error) {
      console.error('❌ Error loading costs:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to load costs';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Add new cost entry
   */
  const addCost = useCallback(async (
    productName: string,
    quantity: number,
    essencePrice: number,
    essenceAmount: number,
    essenceUsed: number,
    alcoholPrice: number,
    alcoholUsed: number,
    bottlePrice: number
  ): Promise<void> => {
    try {
      if (__DEV__) console.log('💰 Adding cost for:', productName);
      setError(null);

      const costData: CreateCostRequest = {
        productName: productName.trim(),
        quantity,
        essencePrice,
        essenceAmount,
        essenceUsed,
        alcoholPrice,
        alcoholUsed,
        bottlePrice,
      };

      const newCost = await costsApi.createCost(costData);

      // Update global state with sorted costs
      const updatedCosts = [newCost, ...globalCosts].sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setGlobalCosts(updatedCosts);
      setCosts(updatedCosts);

      if (__DEV__) console.log('✅ Cost added successfully');
    } catch (error) {
      console.error('❌ Error adding cost:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to add cost';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Delete cost
   */
  const deleteCost = useCallback(async (costId: string): Promise<void> => {
    try {
      if (__DEV__) console.log('💰 Deleting cost:', costId);
      setError(null);

      await costsApi.deleteCost(costId);

      // Update global state
      const updatedCosts = globalCosts.filter(cost => cost.id !== costId);
      setGlobalCosts(updatedCosts);
      setCosts(updatedCosts);

      if (__DEV__) console.log('✅ Cost deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting cost:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to delete cost';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Get cost summary from backend
   */
  const getCostSummary = useCallback(async (): Promise<CostSummary> => {
    try {
      if (__DEV__) console.log('💰 Getting cost summary from backend...');
      setError(null);

      const summary = await costsApi.getCostSummary();

      if (__DEV__) console.log('✅ Cost summary fetched successfully');
      return summary;
    } catch (error) {
      console.error('❌ Error getting cost summary:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to get cost summary';
      setError(errorMessage);
      
      // Return local calculation as fallback
      return calculateLocalCostSummary();
    }
  }, []);

  /**
   * Calculate cost summary locally (fallback)
   */
  const calculateLocalCostSummary = useCallback((): CostSummary => {
    const totalProducts = costs.length;
    const totalCost = costs.reduce((sum, cost) => sum + cost.totalCost, 0);
    const averageCost = totalProducts > 0 ? totalCost / totalProducts : 0;
    const totalEssenceUsed = costs.reduce((sum, cost) => sum + cost.essenceUsed, 0);
    const totalAlcoholUsed = costs.reduce((sum, cost) => sum + cost.alcoholUsed, 0);

    return {
      totalProducts,
      totalCost,
      averageCost,
      totalEssenceUsed,
      totalAlcoholUsed,
    };
  }, [costs]);

  /**
   * Get costs by product name
   */
  const getCostsByProduct = useCallback(async (productName: string): Promise<Cost[]> => {
    try {
      if (__DEV__) console.log('💰 Getting costs for product:', productName);
      setError(null);

      const productCosts = await costsApi.getCostsByProduct(productName);

      if (__DEV__) console.log(`✅ Found ${productCosts.length} costs for product ${productName}`);
      return productCosts;
    } catch (error) {
      console.error('❌ Error getting costs by product:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to get costs by product';
      setError(errorMessage);
      
      // Return local filter as fallback
      return costs.filter(cost => 
        cost.productName.toLowerCase().includes(productName.toLowerCase())
      );
    }
  }, [costs]);

  /**
   * Search costs
   */
  const searchCosts = useCallback(async (query: string): Promise<Cost[]> => {
    try {
      if (__DEV__) console.log('💰 Searching costs:', query);
      setError(null);

      const searchResults = await costsApi.searchCosts(query);

      if (__DEV__) console.log(`✅ Found ${searchResults.length} costs`);
      return searchResults;
    } catch (error) {
      console.error('❌ Error searching costs:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to search costs';
      setError(errorMessage);
      return [];
    }
  }, []);

  /**
   * Refresh costs from backend
   */
  const refreshCosts = useCallback(async (): Promise<void> => {
    await loadCosts();
  }, [loadCosts]);

  /**
   * Get cost by ID
   */
  const getCostById = useCallback((costId: string): Cost | undefined => {
    return costs.find(cost => cost.id === costId);
  }, [costs]);

  /**
   * Get total cost for a specific product
   */
  const getTotalCostForProduct = useCallback((productName: string): number => {
    return costs
      .filter(cost => cost.productName.toLowerCase() === productName.toLowerCase())
      .reduce((sum, cost) => sum + cost.totalCost, 0);
  }, [costs]);

  /**
   * Get average unit cost for a specific product
   */
  const getAverageUnitCostForProduct = useCallback((productName: string): number => {
    const productCosts = costs.filter(cost => 
      cost.productName.toLowerCase() === productName.toLowerCase()
    );
    
    if (productCosts.length === 0) return 0;
    
    const totalUnitCost = productCosts.reduce((sum, cost) => sum + cost.unitCost, 0);
    return totalUnitCost / productCosts.length;
  }, [costs]);

  // Global costs listener for real-time sync
  useEffect(() => {
    const unsubscribe = addGlobalCostsListener((newCosts) => {
      if (__DEV__) console.log('🔄 useCosts received global costs update');
      setCosts(newCosts);
    });

    return unsubscribe;
  }, []);

  // Load costs on mount
  useEffect(() => {
    loadCosts();

    // Set up periodic refresh for real-time sync (every 30 seconds)
    const interval = setInterval(() => {
      if (__DEV__) console.log('🔄 Periodic costs refresh');
      loadCosts();
    }, 30000);

    return () => clearInterval(interval);
  }, [loadCosts]);

  // Debug costs state changes (only in development)
  useEffect(() => {
    if (__DEV__) console.log('🔄 useCosts state changed:', { 
      count: costs.length, 
      loading, 
      error 
    });
  }, [costs, loading, error]);

  return {
    costs,
    loading,
    error,
    addCost,
    deleteCost,
    getCostSummary,
    getCostsByProduct,
    searchCosts,
    refreshCosts,
    getCostById,
    getTotalCostForProduct,
    getAverageUnitCostForProduct,
  };
}
