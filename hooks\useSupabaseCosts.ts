import { offlineQueue } from '@/lib/offlineQueue';
import { supabase } from '@/lib/supabase';
import { Cost } from '@/types/Cost';
import { useFocusEffect } from '@react-navigation/native';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNetworkStatus } from './useNetworkStatus';
import { useOptimisticUpdate } from './useOptimisticUpdate';
import { usePagination } from './usePagination';

export function useSupabaseCosts() {
  const [costs, setCosts] = useState<Cost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const isMounted = useRef(true);
  const lastFetchTime = useRef<number>(0);
  const FETCH_THROTTLE_MS = 2000;
  const { isOffline } = useNetworkStatus();
  const {
    currentPage,
    hasMore,
    isFetchingMore,
    setHasMore,
    setFetchingMore,
    incrementPage,
    getRange,
    resetPagination,
    createAbortController
  } = usePagination();

  const {
    addOptimisticItem,
    confirmOptimisticItem,
    removeOptimisticItem,
    isPending
  } = useOptimisticUpdate<Cost>({
    onError: (error) => {
      setToastMessage('Maliyet kaydedilemedi, tekrar deneyin.');
      setToastVisible(true);
    }
  });

  const fetchCosts = useCallback(async (silent = false, isLoadMore = false) => {
    if (!silent) {
      if (isLoadMore) {
        setFetchingMore(true);
      } else {
        setLoading(true);
      }
    }

    const now = Date.now();
    if (now - lastFetchTime.current < FETCH_THROTTLE_MS) {
      if (!silent) {
        if (isLoadMore) {
          setFetchingMore(false);
        } else {
          setLoading(false);
        }
      }
      return;
    }
    lastFetchTime.current = now;

    const abortController = createAbortController();
    const { start, end } = getRange();

    try {
      const { data, error: fetchError, count } = await supabase
        .from('costs')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(start, end)
        .abortSignal(abortController.signal);

      if (fetchError) {
        throw fetchError;
      }

      if (data) {
        const costsData = data.map((cost: any) => ({
          id: cost.id,
          product_name: cost.product_name,
          quantity: cost.quantity,
          essence_price: cost.essence_price,
          essence_amount: cost.essence_amount,
          essence_used: cost.essence_used,
          alcohol_price: cost.alcohol_price,
          alcohol_used: cost.alcohol_used,
          bottle_price: cost.bottle_price,
          essence_cost: cost.essence_cost,
          alcohol_cost: cost.alcohol_cost,
          unit_cost: cost.unit_cost,
          total_cost: cost.total_cost,
          created_at: cost.created_at,
          isOptimistic: false
        }));

        if (isLoadMore) {
          setCosts(prev => [...prev, ...costsData]);
        } else {
          setCosts(costsData);
        }

        setHasMore(count ? count > end + 1 : false);
      }
    } catch (err) {
      console.error('Error fetching costs:', err);
      if (!silent) {
        setError(err instanceof Error ? err.message : 'Maliyetler yüklenirken bir hata oluştu');
      }
    } finally {
      if (!silent) {
        if (isLoadMore) {
          setFetchingMore(false);
        } else {
          setLoading(false);
        }
      }
    }
  }, [createAbortController, getRange, setFetchingMore, setHasMore]);

  const silentRefresh = useCallback(
    debounce(() => {
      fetchCosts(true);
    }, 1000),
    [fetchCosts]
  );

  const addCost = useCallback(async (costData: {
    product_name: string;
    quantity: number;
    essence_price: number;
    essence_amount: number;
    essence_used: number;
    alcohol_price: number;
    alcohol_used: number;
    bottle_price: number;
  }) => {
    let optimisticCost: Cost | undefined;
    try {
      // Calculate costs
      const essenceCost = (costData.essence_price * costData.essence_used) / costData.essence_amount;
      const alcoholCost = costData.alcohol_price * costData.alcohol_used;
      const unitCost = (essenceCost + alcoholCost + costData.bottle_price) / costData.quantity;
      const totalCost = unitCost * costData.quantity;

      if (isOffline) {
        await offlineQueue.addOperation({
          type: 'add',
          table: 'costs',
          data: {
            ...costData,
            essence_cost: essenceCost,
            alcohol_cost: alcoholCost,
            unit_cost: unitCost,
            total_cost: totalCost
          }
        });
        
        optimisticCost = addOptimisticItem({
          ...costData,
          essence_cost: essenceCost,
          alcohol_cost: alcoholCost,
          unit_cost: unitCost,
          total_cost: totalCost,
          created_at: new Date().toISOString()
        });
        
        setCosts(prev => [...prev, optimisticCost as Cost]);
        return true;
      }

      // Add optimistic item immediately
      optimisticCost = addOptimisticItem({
        ...costData,
        essence_cost: essenceCost,
        alcohol_cost: alcoholCost,
        unit_cost: unitCost,
        total_cost: totalCost,
        created_at: new Date().toISOString()
      });
      
      setCosts(prev => [...prev, optimisticCost as Cost]);

      const { data, error: insertError } = await supabase
        .from('costs')
        .insert([{
          ...costData,
          essence_cost: essenceCost,
          alcohol_cost: alcoholCost,
          unit_cost: unitCost,
          total_cost: totalCost
        }])
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      if (data) {
        const newCost: Cost = {
          id: data.id,
          product_name: data.product_name,
          quantity: data.quantity,
          essence_price: data.essence_price,
          essence_amount: data.essence_amount,
          essence_used: data.essence_used,
          alcohol_price: data.alcohol_price,
          alcohol_used: data.alcohol_used,
          bottle_price: data.bottle_price,
          essence_cost: data.essence_cost,
          alcohol_cost: data.alcohol_cost,
          unit_cost: data.unit_cost,
          total_cost: data.total_cost,
          created_at: data.created_at,
          isOptimistic: false
        };
        
        // Replace optimistic item with real one
        setCosts(prev => prev.map(c => 
          c.id === optimisticCost?.id ? newCost : c
        ));
        
        if (optimisticCost?.id) {
          confirmOptimisticItem(optimisticCost.id, newCost);
        }
        silentRefresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error adding cost:', err);
      setError(err instanceof Error ? err.message : 'Maliyet eklenirken bir hata oluştu');
      
      // Remove optimistic item on error
      if (optimisticCost?.id) {
        removeOptimisticItem(optimisticCost.id, err as Error);
        setCosts(prev => prev.filter(c => c.id !== optimisticCost?.id));
      }
      
      return false;
    }
  }, [isOffline, silentRefresh, addOptimisticItem, confirmOptimisticItem, removeOptimisticItem]);

  const getCostSummary = useCallback(() => {
    const totalCost = costs.reduce((sum, cost) => sum + cost.total_cost, 0);
    const totalQuantity = costs.reduce((sum, cost) => sum + cost.quantity, 0);
    const averageUnitCost = totalQuantity > 0 ? totalCost / totalQuantity : 0;

    return {
      totalCost,
      totalQuantity,
      averageUnitCost
    };
  }, [costs]);

  useEffect(() => {
    isMounted.current = true;
    fetchCosts();

    const subscription = supabase
      .channel('costs_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'costs' }, () => {
        if (isMounted.current) {
          silentRefresh();
        }
      })
      .subscribe();

    return () => {
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [fetchCosts, silentRefresh]);

  useFocusEffect(
    useCallback(() => {
      resetPagination();
      fetchCosts();
    }, [fetchCosts, resetPagination])
  );

  useEffect(() => {
    if (!isOffline) {
      offlineQueue.processQueue();
    }
  }, [isOffline]);

  return {
    costs,
    loading,
    error,
    isRefreshing,
    hasMore,
    isFetchingMore,
    addCost,
    getCostSummary,
    refreshCosts: () => {
      resetPagination();
      fetchCosts(false);
    },
    loadMoreCosts: () => {
      if (hasMore && !isFetchingMore) {
        fetchCosts(true, true);
      }
    },
    silentRefresh,
    isPending,
    toastVisible,
    setToastVisible,
    toastMessage
  };
}
