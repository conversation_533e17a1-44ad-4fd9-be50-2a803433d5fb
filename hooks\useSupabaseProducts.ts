import { offlineQueue } from '@/lib/offlineQueue';
import { supabase } from '@/lib/supabase';
import { retryableSubscribe } from '@/lib/supabase/retrySubscription';
import { Product } from '@/types/Product';
import { useFocusEffect } from '@react-navigation/native';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNetworkStatus } from './useNetworkStatus';
import { useOptimisticUpdate } from './useOptimisticUpdate';
import { usePagination } from './usePagination';

export function useSupabaseProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const isMounted = useRef(true);
  const lastFetchTime = useRef<number>(0);
  const FETCH_THROTTLE_MS = 2000; // 2 seconds minimum between fetches
  const { isOffline } = useNetworkStatus();
  const {
    currentPage,
    hasMore,
    isFetchingMore,
    setHasMore,
    setFetchingMore,
    incrementPage,
    getRange,
    resetPagination,
    createAbortController
  } = usePagination();

  const {
    addOptimisticItem,
    confirmOptimisticItem,
    removeOptimisticItem,
    isPending
  } = useOptimisticUpdate<Product>({
    onError: (error) => {
      setToastMessage('Kaydedilemedi, tekrar deneyin');
      setToastVisible(true);
    }
  });

  const fetchProducts = useCallback(async (silent = false, isLoadMore = false) => {
    if (isOffline) {
      console.log('📱 Offline mode: Skipping fetch');
      return;
    }

    if (!silent && !isLoadMore) {
      setLoading(true);
    }
    if (isLoadMore) {
      setFetchingMore(true);
    }
    setError(null);

    try {
      const now = Date.now();
      if (now - lastFetchTime.current < FETCH_THROTTLE_MS) {
        return; // Skip if too soon
      }
      lastFetchTime.current = now;

      const { start, end } = getRange();
      const controller = createAbortController();

      const { data, error: fetchError, count } = await supabase
        .from('products')
        .select('*', { count: 'exact' })
        .order('name', { ascending: true })
        .range(start, end)
        .abortSignal(controller.signal);

      if (fetchError) {
        throw fetchError;
      }

      if (!isMounted.current) return;

      const transformedProducts = (data || []).map(product => ({
        id: product.id,
        name: product.name,
        stock: product.stock,
        description: product.description || '',
        created_at: product.created_at
      }));

      // Update hasMore based on total count
      const totalCount = count || 0;
      setHasMore(start + transformedProducts.length < totalCount);

      setProducts(prev => {
        if (isLoadMore) {
          return [...prev, ...transformedProducts];
        }
        return transformedProducts;
      });

      if (isLoadMore) {
        incrementPage();
      }
    } catch (err) {
      if (!isMounted.current) return;
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err.message : 'Ürünler yüklenirken bir hata oluştu');
    } finally {
      if (!silent && !isLoadMore) {
        setLoading(false);
      }
      if (isLoadMore) {
        setFetchingMore(false);
      }
    }
  }, [isOffline, getRange, setHasMore, setFetchingMore, incrementPage, createAbortController]);

  // Debounced silent refresh
  const silentRefresh = useCallback(
    debounce(() => {
      fetchProducts(true);
    }, 1000),
    [fetchProducts]
  );

  const addProduct = useCallback(async (productData: { name: string; stock: number; description?: string }) => {
    let optimisticProduct: Product | undefined;
    try {
      if (isOffline) {
        await offlineQueue.addOperation({
          type: 'add',
          table: 'products',
          data: productData
        });
        
        optimisticProduct = addOptimisticItem({
          name: productData.name,
          stock: productData.stock,
          description: productData.description || '',
          created_at: new Date().toISOString()
        });
        
        setProducts(prev => [...prev, optimisticProduct as Product]);
        return true;
      }

      // Add optimistic item immediately
      optimisticProduct = addOptimisticItem({
        name: productData.name,
        stock: productData.stock,
        description: productData.description || '',
        created_at: new Date().toISOString()
      });
      
      setProducts(prev => [...prev, optimisticProduct as Product]);

      const { data, error: insertError } = await supabase
        .from('products')
        .insert([{
          name: productData.name,
          stock: productData.stock,
          description: productData.description || ''
        }])
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      if (data) {
        const newProduct: Product = {
          id: data.id,
          name: data.name,
          stock: data.stock,
          description: data.description || '',
          created_at: data.created_at,
          isOptimistic: false
        };
        
        // Replace optimistic item with real one
        setProducts(prev => prev.map(p => 
          p.id === optimisticProduct?.id ? newProduct : p
        ));
        
        if (optimisticProduct?.id) {
          confirmOptimisticItem(optimisticProduct.id, newProduct);
        }
        silentRefresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error adding product:', err);
      setError(err instanceof Error ? err.message : 'Ürün eklenirken bir hata oluştu');
      
      // Remove optimistic item on error
      if (optimisticProduct?.id) {
        removeOptimisticItem(optimisticProduct.id, err as Error);
        setProducts(prev => prev.filter(p => p.id !== optimisticProduct?.id));
      }
      
      return false;
    }
  }, [isOffline, silentRefresh, addOptimisticItem, confirmOptimisticItem, removeOptimisticItem]);

  const deleteProduct = useCallback(async (productId: string) => {
    try {
      const { error: deleteError } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (deleteError) {
        throw deleteError;
      }

      setProducts(prev => prev.filter(product => product.id !== productId));
      silentRefresh(); // Trigger silent refresh after deleting
    } catch (err) {
      console.error('Error deleting product:', err);
      setError(err instanceof Error ? err.message : 'Ürün silinirken bir hata oluştu');
      throw err;
    }
  }, [silentRefresh]);

  const refreshProducts = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await fetchProducts();
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchProducts]);

  // Reset pagination and fetch first page when screen is focused
  useFocusEffect(
    useCallback(() => {
      resetPagination();
      fetchProducts(false);
    }, [resetPagination, fetchProducts])
  );

  // Process offline queue when coming back online
  useEffect(() => {
    if (!isOffline) {
      const processOfflineQueue = async () => {
        const operations = await offlineQueue.getOperations();
        for (const operation of operations) {
          if (operation.table === 'products') {
            try {
              switch (operation.type) {
                case 'add':
                  await supabase
                    .from('products')
                    .insert([operation.data])
                    .select()
                    .single();
                  break;
                // Add other operation types as needed
              }
              await offlineQueue.removeOperation(operation.id);
            } catch (error) {
              console.error('Error processing offline operation:', error);
            }
          }
        }
      };
      processOfflineQueue();
    }
  }, [isOffline]);

  useEffect(() => {
    isMounted.current = true;
    fetchProducts();

    const { unsubscribe } = retryableSubscribe({
      channel: 'products_changes',
      event: '*',
      table: 'products',
      callback: () => {
        if (isMounted.current) {
          silentRefresh();
        }
      }
    });

    return () => {
      isMounted.current = false;
      unsubscribe();
    };
  }, [fetchProducts, silentRefresh]);

  return {
    products,
    loading,
    isLoading: loading, // Alias for compatibility
    error,
    isRefreshing,
    hasMore,
    isFetchingMore,
    addProduct,
    deleteProduct,
    refreshProducts: async () => {
      resetPagination();
      await fetchProducts(false);
    },
    loadMoreProducts: () => {
      if (hasMore && !isFetchingMore) {
        fetchProducts(true, true);
      }
    },
    silentRefresh,
    isPending,
    toastVisible,
    setToastVisible,
    toastMessage
  };
}
