import { ThemedText } from '@/components/ThemedText';
import React, { useRef, useState } from 'react';
import { Animated, GestureResponderEvent, StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';

interface RippleButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
  textStyle?: any;
  disabled?: boolean;
  rippleColor?: string;
  children?: React.ReactNode;
}

export function RippleButton({
  title,
  onPress,
  style,
  textStyle,
  disabled = false,
  rippleColor = 'rgba(255, 255, 255, 0.3)',
  children,
}: RippleButtonProps) {
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number; scale: Animated.Value; opacity: Animated.Value }>>([]);
  const rippleId = useRef(0);

  const createRipple = (event: GestureResponderEvent) => {
    if (disabled) return;

    const { locationX, locationY } = event.nativeEvent;
    const id = rippleId.current++;
    
    const scale = new Animated.Value(0);
    const opacity = new Animated.Value(1);

    const newRipple = {
      id,
      x: locationX,
      y: locationY,
      scale,
      opacity,
    };

    setRipples(prev => [...prev, newRipple]);

    // Animate ripple
    Animated.parallel([
      Animated.timing(scale, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Remove ripple after animation
      setRipples(prev => prev.filter(ripple => ripple.id !== id));
    });
  };

  const handlePress = (event: GestureResponderEvent) => {
    createRipple(event);
    onPress();
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {children || (
          <ThemedText style={[styles.text, textStyle]}>
            {title}
          </ThemedText>
        )}
      </View>
      
      {/* Ripple Effects */}
      <View style={styles.rippleContainer}>
        {ripples.map((ripple) => (
          <Animated.View
            key={ripple.id}
            style={[
              styles.ripple,
              {
                left: ripple.x - 50,
                top: ripple.y - 50,
                backgroundColor: rippleColor,
                transform: [{ scale: ripple.scale }],
                opacity: ripple.opacity,
              },
            ]}
          />
        ))}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
  },
  content: {
    zIndex: 1,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  rippleContainer: {
    flex: 1,
    pointerEvents: 'none',
  },
  ripple: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
});
