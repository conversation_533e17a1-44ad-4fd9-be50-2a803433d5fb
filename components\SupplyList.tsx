import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Supply, SupplyStatus } from '@/types/Supply';
import { useState } from 'react';
import { Alert, FlatList, Modal, StyleSheet, TouchableOpacity } from 'react-native';

interface SupplyListProps {
  supplies: Supply[];
  onUpdateSupplyStatus: (supplyId: string, status: SupplyStatus, actualDeliveryDate?: Date) => Promise<void>;
  onDeleteSupply: (supplyId: string) => Promise<void>;
}

export function SupplyList({ supplies, onUpdateSupplyStatus, onDeleteSupply }: SupplyListProps) {
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedSupply, setSelectedSupply] = useState<Supply | null>(null);

  const borderColor = useThemeColor({ light: '#eee', dark: '#333' }, 'text');
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR');
  };

  const getStatusColor = (status: SupplyStatus) => {
    switch (status) {
      case 'ordered':
        return '#ff9500';
      case 'shipped':
        return '#007AFF';
      case 'delivered':
        return '#34c759';
      default:
        return '#8e8e93';
    }
  };

  const getStatusText = (status: SupplyStatus) => {
    switch (status) {
      case 'ordered':
        return 'Sipariş Verildi';
      case 'shipped':
        return 'Kargoda';
      case 'delivered':
        return 'Teslim Alındı';
      default:
        return 'Bilinmiyor';
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'essence':
        return 'Esans';
      case 'alcohol':
        return 'Alkol';
      case 'bottle':
        return 'Şişe';
      case 'packaging':
        return 'Ambalaj';
      case 'other':
        return 'Diğer';
      default:
        return category;
    }
  };

  const handleStatusChange = (supply: Supply) => {
    setSelectedSupply(supply);
    setShowStatusModal(true);
  };

  const handleStatusSelect = async (status: SupplyStatus) => {
    if (!selectedSupply) return;

    setShowStatusModal(false);

    const actualDeliveryDate = status === 'delivered' ? new Date() : undefined;
    await onUpdateSupplyStatus(selectedSupply.id, status, actualDeliveryDate);
    setSelectedSupply(null);
  };

  const handleDeleteSupply = (supply: Supply) => {
    Alert.alert(
      'Tedarik Kaydını Sil',
      `"${supply.itemName}" tedarik kaydını silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => onDeleteSupply(supply.id)
        },
      ]
    );
  };

  const renderSupply = ({ item }: { item: Supply }) => (
    <ThemedView style={[styles.supplyItem, { borderColor }]}>
      <ThemedView style={styles.supplyHeader}>
        <ThemedView style={styles.supplyInfo}>
          <ThemedText type="defaultSemiBold" style={styles.itemName}>
            {item.itemName}
          </ThemedText>
          <ThemedText style={styles.categoryText}>
            {getCategoryText(item.category)} • {item.supplier}
          </ThemedText>
          <ThemedText style={styles.quantityText}>
            {item.quantity} {item.unit} × {item.unitPrice.toFixed(2)}₺
          </ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.priceContainer}>
          <ThemedText style={styles.priceLabel}>Toplam</ThemedText>
          <ThemedText style={styles.totalPrice}>
            {item.totalPrice.toFixed(2)} ₺
          </ThemedText>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.supplyDetails}>
        <ThemedView style={styles.detailRow}>
          <ThemedText style={styles.detailLabel}>Sipariş Tarihi:</ThemedText>
          <ThemedText style={styles.detailValue}>{formatDate(item.orderDate)}</ThemedText>
        </ThemedView>
        
        {item.expectedDeliveryDate && (
          <ThemedView style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Tahmini Teslimat:</ThemedText>
            <ThemedText style={styles.detailValue}>{formatDate(item.expectedDeliveryDate)}</ThemedText>
          </ThemedView>
        )}
        
        {item.actualDeliveryDate && (
          <ThemedView style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Gerçek Teslimat:</ThemedText>
            <ThemedText style={styles.detailValue}>{formatDate(item.actualDeliveryDate)}</ThemedText>
          </ThemedView>
        )}
        
        {item.notes && (
          <ThemedView style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Notlar:</ThemedText>
            <ThemedText style={styles.detailValue}>{item.notes}</ThemedText>
          </ThemedView>
        )}
      </ThemedView>

      <ThemedView style={styles.supplyActions}>
        <TouchableOpacity
          style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
          onPress={() => handleStatusChange(item)}
        >
          <ThemedText style={styles.statusText}>
            {getStatusText(item.status)}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteSupply(item)}
        >
          <ThemedText style={styles.deleteButtonText}>Sil</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ThemedView>
  );

  const renderEmptyComponent = () => (
    <ThemedView style={styles.emptyContainer}>
      <ThemedText style={styles.emptyText}>
        Henüz tedarik kaydı bulunmuyor.{'\n'}
        Yeni tedarik kaydı eklemek için yukarıdaki formu kullanın.
      </ThemedText>
    </ThemedView>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        Tedarik Kayıtları ({supplies.length})
      </ThemedText>

      <FlatList
        data={supplies}
        renderItem={renderSupply}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={supplies.length === 0 ? styles.emptyList : styles.listContent}
      />

      {/* Status Selection Modal */}
      <Modal
        visible={showStatusModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowStatusModal(false)}
      >
        <ThemedView style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { backgroundColor }]}>
            <ThemedText type="subtitle" style={styles.modalTitle}>
              Durum Güncelle
            </ThemedText>

            <ThemedText style={styles.modalSubtitle}>
              {selectedSupply?.itemName} - {selectedSupply?.supplier}
            </ThemedText>

            <ThemedView style={styles.statusOptions}>
              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#ff9500' }]}
                onPress={() => handleStatusSelect('ordered')}
              >
                <ThemedText style={styles.statusOptionText}>Sipariş Verildi</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#007AFF' }]}
                onPress={() => handleStatusSelect('shipped')}
              >
                <ThemedText style={styles.statusOptionText}>Kargoda</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.statusOption, { backgroundColor: '#34c759' }]}
                onPress={() => handleStatusSelect('delivered')}
              >
                <ThemedText style={styles.statusOptionText}>Teslim Alındı</ThemedText>
              </TouchableOpacity>
            </ThemedView>

            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowStatusModal(false)}
            >
              <ThemedText style={styles.cancelButtonText}>İptal</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  supplyItem: {
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  supplyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  supplyInfo: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 16,
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  quantityText: {
    fontSize: 14,
    opacity: 0.8,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 2,
  },
  totalPrice: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  supplyDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 14,
    opacity: 0.7,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    flex: 1,
    textAlign: 'right',
  },
  supplyActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  deleteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#ff3b30',
    borderRadius: 6,
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  emptyList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    minWidth: 280,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  statusOptions: {
    marginBottom: 20,
  },
  statusOption: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  statusOptionText: {
    color: '#fff',
    fontWeight: '600',
  },
  modalButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#8e8e93',
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
