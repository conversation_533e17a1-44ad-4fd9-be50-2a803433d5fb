export type UserRole = 'admin' | 'manager' | 'employee' | 'viewer';

export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: UserRole;
  avatar?: string;
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
  permissions: UserPermissions;
}

export interface UserPermissions {
  // Product permissions
  canViewProducts: boolean;
  canAddProducts: boolean;
  canEditProducts: boolean;
  canDeleteProducts: boolean;
  canManageProducts?: boolean; // Alias for compatibility

  // Order permissions
  canViewOrders: boolean;
  canCreateOrders: boolean;
  canUpdateOrderStatus: boolean;
  canDeleteOrders: boolean;
  canManageOrders?: boolean; // Alias for compatibility

  // Cost permissions
  canViewCosts: boolean;
  canAddCosts: boolean;
  canEditCosts: boolean;
  canDeleteCosts: boolean;
  canManageCosts?: boolean; // Alias for compatibility

  // Report permissions
  canViewReports: boolean;
  canExportReports: boolean;
  canViewFinancials: boolean;

  // User management permissions
  canManageUsers: boolean;
  canViewUserActivity: boolean;
  canChangeUserRoles: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  refreshToken?: string;
  message?: string;
}

// Default permissions for each role
export const DEFAULT_PERMISSIONS: Record<UserRole, UserPermissions> = {
  admin: {
    canViewProducts: true,
    canAddProducts: true,
    canEditProducts: true,
    canDeleteProducts: true,
    canManageProducts: true,
    canViewOrders: true,
    canCreateOrders: true,
    canUpdateOrderStatus: true,
    canDeleteOrders: true,
    canManageOrders: true,
    canViewCosts: true,
    canAddCosts: true,
    canEditCosts: true,
    canDeleteCosts: true,
    canManageCosts: true,
    canViewReports: true,
    canExportReports: true,
    canViewFinancials: true,
    canManageUsers: true,
    canViewUserActivity: true,
    canChangeUserRoles: true,
  },
  manager: {
    canViewProducts: true,
    canAddProducts: true,
    canEditProducts: true,
    canDeleteProducts: false,
    canManageProducts: true,
    canViewOrders: true,
    canCreateOrders: true,
    canUpdateOrderStatus: true,
    canDeleteOrders: false,
    canManageOrders: true,
    canViewCosts: true,
    canAddCosts: true,
    canEditCosts: true,
    canDeleteCosts: false,
    canManageCosts: true,
    canViewReports: true,
    canExportReports: true,
    canViewFinancials: true,
    canManageUsers: false,
    canViewUserActivity: true,
    canChangeUserRoles: false,
  },
  employee: {
    canViewProducts: true,
    canAddProducts: true,
    canEditProducts: false,
    canDeleteProducts: false,
    canManageProducts: false,
    canViewOrders: true,
    canCreateOrders: true,
    canUpdateOrderStatus: true,
    canDeleteOrders: false,
    canManageOrders: false,
    canViewCosts: false,
    canAddCosts: false,
    canEditCosts: false,
    canDeleteCosts: false,
    canManageCosts: false,
    canViewReports: false,
    canExportReports: false,
    canViewFinancials: false,
    canManageUsers: false,
    canViewUserActivity: false,
    canChangeUserRoles: false,
  },
  viewer: {
    canViewProducts: true,
    canAddProducts: false,
    canEditProducts: false,
    canDeleteProducts: false,
    canManageProducts: false,
    canViewOrders: true,
    canCreateOrders: false,
    canUpdateOrderStatus: false,
    canDeleteOrders: false,
    canManageOrders: false,
    canViewCosts: false,
    canAddCosts: false,
    canEditCosts: false,
    canDeleteCosts: false,
    canManageCosts: false,
    canViewReports: true,
    canExportReports: false,
    canViewFinancials: false,
    canManageUsers: false,
    canViewUserActivity: false,
    canChangeUserRoles: false,
  },
};

export const ROLE_LABELS: Record<UserRole, string> = {
  admin: 'Yönetici',
  manager: 'Müdür',
  employee: 'Çalışan',
  viewer: 'Görüntüleyici',
};

export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  admin: 'Tüm yetkilere sahip sistem yöneticisi',
  manager: 'Operasyonel yönetim yetkilerine sahip müdür',
  employee: 'Günlük işlemler için temel yetkiler',
  viewer: 'Sadece görüntüleme yetkisi',
};
