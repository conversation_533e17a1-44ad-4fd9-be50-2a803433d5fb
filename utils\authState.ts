// Global auth state management
let globalAuthState = {
  isAuthenticated: false,
  user: null as any,
  isLoading: true,
};

const listeners: Array<() => void> = [];

export const authStateManager = {
  getState: () => globalAuthState,
  
  setState: (newState: Partial<typeof globalAuthState>) => {
    globalAuthState = { ...globalAuthState, ...newState };
    if (__DEV__) console.log('Global auth state updated:', globalAuthState);

    // Notify all listeners
    listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        if (__DEV__) console.error('Auth state listener error:', error);
      }
    });
  },
  
  subscribe: (listener: () => void) => {
    listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  },
  
  // Force page reload after auth change (web only)
  forceReload: () => {
    setTimeout(() => {
      if (typeof window !== 'undefined' && window.location) {
        console.log('Forcing page reload due to auth change...');
        try {
          window.location.reload();
        } catch (error) {
          console.warn('Failed to reload page:', error);
        }
      } else {
        console.log('Page reload not available on this platform');
      }
    }, 100);
  }
};
