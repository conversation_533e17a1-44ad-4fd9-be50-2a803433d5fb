import { ThemedView } from '@/components/ThemedView';
import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Image, Platform, StyleSheet } from 'react-native';

const { width, height } = Dimensions.get('window');

interface SplashScreenLogoProps {
  onAnimationFinish?: () => void;
}

export function SplashScreenLogo({ onAnimationFinish }: SplashScreenLogoProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const [isLayoutReady, setIsLayoutReady] = React.useState(false);

  useEffect(() => {
    console.log('🎬 Mobile-Friendly Logo SplashScreen mounted - starting animation');

    // Prevent auto-hide of native splash screen
    const preventAutoHide = async () => {
      try {
        const SplashScreen = require('expo-splash-screen');
        await SplashScreen.preventAutoHideAsync();
        console.log('🎬 Native splash screen prevented from auto-hiding');
      } catch (error) {
        console.warn('⚠️ Failed to prevent splash screen auto-hide:', error);
      }
    };

    preventAutoHide();

    // Mobile-optimized animation sequence
    const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
    const animationDuration = isMobile ? 800 : 1000; // Faster on mobile

    // Start fade-in animation for full screen logo
    const animationSequence = Animated.parallel([
      // Fade in animation (faster on mobile)
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: animationDuration, // Mobile optimized duration
        useNativeDriver: true,
      }),
      // Scale animation for zoom effect (faster on mobile)
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: isMobile ? 50 : 40, // Higher tension for mobile
        friction: isMobile ? 5 : 6,  // Lower friction for mobile
        useNativeDriver: true,
      }),
    ]);

    animationSequence.start(() => {
      console.log('🎬 Mobile-friendly logo animation completed');

      // Wait a bit after animation, then navigate (mobile optimized)
      const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
      const displayTime = isMobile ? 400 : 600; // Much shorter on mobile

      setTimeout(() => {
        handleAnimationFinish();
      }, displayTime); // Mobile optimized display time
    });

    // Fallback timer for mobile-friendly logo (aggressive)
    const fallbackTimer = setTimeout(() => {
      console.log('🎬 Mobile-friendly logo fallback timer triggered - FORCING FINISH');
      handleAnimationFinish();
    }, 3000); // 3 seconds max - GUARANTEED finish

    // Additional emergency timer
    const emergencyTimer = setTimeout(() => {
      console.log('🚨 EMERGENCY: Splash screen stuck - forcing callback');
      if (onAnimationFinish) {
        onAnimationFinish();
      }
    }, 5000); // 5 seconds emergency

    return () => {
      clearTimeout(fallbackTimer);
      clearTimeout(emergencyTimer);
    };
  }, []);

  const handleAnimationFinish = () => {
    console.log('🎬 Mobile-friendly logo splash animation finished');

    // Hide native splash screen
    const hideNativeSplash = async () => {
      try {
        const SplashScreen = require('expo-splash-screen');
        await SplashScreen.hideAsync();
        console.log('🎬 Native splash screen hidden');
      } catch (error) {
        console.warn('⚠️ Failed to hide native splash screen:', error);
      }
    };

    hideNativeSplash();

    // Call callback if provided
    if (onAnimationFinish) {
      onAnimationFinish();
    }

    // Fade-out animation for smooth transition (mobile optimized)
    console.log('🎬 Starting fade-out animation for smooth transition');
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200, // Very fast fade-out for mobile (200ms)
      useNativeDriver: true,
    }).start(() => {
      console.log('🎬 Fade-out animation completed - navigating');
      // No navigation needed - tab layout handles authentication flow
    });
  };

  // onLayoutRootView - GUARANTEED to be called when view is ready
  const onLayoutRootView = React.useCallback(() => {
    if (!isLayoutReady) {
      console.log('🎬 onLayoutRootView called - splash screen is ready');
      setIsLayoutReady(true);

      // Additional safety: call finish after layout is ready
      setTimeout(() => {
        console.log('🎬 Layout ready - ensuring splash finishes');
        if (onAnimationFinish) {
          onAnimationFinish();
        }
      }, 1000);
    }
  }, [isLayoutReady, onAnimationFinish]);

  return (
    <ThemedView style={styles.container} onLayout={onLayoutRootView}>
      {/* Mobile-Friendly Animated Logo */}
      <Animated.View
        style={[
          styles.fullScreenContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >

        <Image
          source={require('@/assets/images/logo.png')}
          style={styles.fullScreenLogo}
          resizeMode="contain"
        />
      </Animated.View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden', // Prevent any overflow causing white edges
  },
  fullScreenContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        paddingHorizontal: 0, // No padding on mobile
        paddingVertical: 0,
      },
      android: {
        paddingHorizontal: 0, // No padding on mobile
        paddingVertical: 0,
      },
      web: {
        paddingHorizontal: 20, // Keep padding on web
        paddingVertical: 40,
      },
    }),
  },
  fullScreenLogo: {
    ...Platform.select({
      ios: {
        width: width * 0.9, // 90% of screen width for mobile
        height: height * 0.7, // 70% of screen height for mobile
        maxWidth: 500,
        maxHeight: 500,
      },
      android: {
        width: width * 0.9, // 90% of screen width for mobile
        height: height * 0.7, // 70% of screen height for mobile
        maxWidth: 500,
        maxHeight: 500,
      },
      web: {
        width: width * 0.8, // 80% of screen width for web
        height: height * 0.6, // 60% of screen height for web
        maxWidth: 400,
        maxHeight: 400,
      },
    }),
  },

});
