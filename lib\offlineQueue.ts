import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';

const QUEUE_STORAGE_KEY = '@offline_queue';

interface QueuedOperation {
  id: string;
  type: 'add' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
}

class OfflineQueue {
  private queue: QueuedOperation[] = [];

  constructor() {
    this.loadQueue();
  }

  private async loadQueue() {
    try {
      const storedQueue = await AsyncStorage.getItem(QUEUE_STORAGE_KEY);
      if (storedQueue) {
        this.queue = JSON.parse(storedQueue);
      }
    } catch (error) {
      console.error('Error loading offline queue:', error);
    }
  }

  private async saveQueue() {
    try {
      await AsyncStorage.setItem(QUEUE_STORAGE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.error('Error saving offline queue:', error);
    }
  }

  async addOperation(operation: Omit<QueuedOperation, 'id' | 'timestamp'>) {
    const queuedOperation: QueuedOperation = {
      ...operation,
      id: Date.now().toString(),
      timestamp: Date.now()
    };

    this.queue.push(queuedOperation);
    await this.saveQueue();
  }

  async getOperations(): Promise<QueuedOperation[]> {
    await this.loadQueue();
    return this.queue;
  }

  async removeOperation(id: string) {
    this.queue = this.queue.filter(op => op.id !== id);
    await this.saveQueue();
  }

  async clearQueue() {
    this.queue = [];
    await this.saveQueue();
  }

  async processQueue(): Promise<void> {
    const operations = await this.getOperations();
    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'add':
            await supabase
              .from(operation.table)
              .insert([operation.data])
              .select()
              .single();
            break;
          case 'update':
            await supabase
              .from(operation.table)
              .update(operation.data)
              .eq('id', operation.data.id);
            break;
          case 'delete':
            await supabase
              .from(operation.table)
              .delete()
              .eq('id', operation.data.id);
            break;
        }
        await this.removeOperation(operation.id);
      } catch (error) {
        console.error('Error processing offline operation:', error);
      }
    }
  }
}

export const offlineQueue = new OfflineQueue(); 