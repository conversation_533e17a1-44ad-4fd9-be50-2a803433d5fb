import { Product } from '@/types/Product';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

const PRODUCTS_STORAGE_KEY = '@inventory_products';

export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Load products from storage
  useEffect(() => {
    loadProducts();

    // Set up interval to refresh products every 2 seconds
    const interval = setInterval(() => {
      loadProducts();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const loadProducts = async () => {
    try {
      const storedProducts = await AsyncStorage.getItem(PRODUCTS_STORAGE_KEY);
      if (storedProducts) {
        const parsedProducts = JSON.parse(storedProducts).map((product: any) => ({
          ...product,
          createdAt: new Date(product.createdAt),
        }));
        setProducts(parsedProducts);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveProducts = async (newProducts: Product[]) => {
    try {
      await AsyncStorage.setItem(PRODUCTS_STORAGE_KEY, JSON.stringify(newProducts));
      setProducts(newProducts);
    } catch (error) {
      console.error('Error saving products:', error);
    }
  };

  const addProduct = async (name: string, stock: number) => {
    const newProduct: Product = {
      id: Date.now().toString(),
      name: name.trim(),
      stock,
      createdAt: new Date(),
    };

    const updatedProducts = [...products, newProduct];
    await saveProducts(updatedProducts);
  };

  const deleteProduct = async (productId: string) => {
    const updatedProducts = products.filter(product => product.id !== productId);
    await saveProducts(updatedProducts);
  };

  const updateProductStock = async (productId: string, newStock: number) => {
    const updatedProducts = products.map(product =>
      product.id === productId ? { ...product, stock: newStock } : product
    );
    await saveProducts(updatedProducts);
  };

  return {
    products,
    loading,
    addProduct,
    deleteProduct,
    updateProductStock,
    refreshProducts: loadProducts,
  };
}
