import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { UserProfile } from '@/components/UserProfile';
import { useAuth } from '@/hooks/useAuth';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Dimensions, Modal, StyleSheet, TouchableOpacity } from 'react-native';

interface MenuItemProps {
  title: string;
  icon: string;
  route: string;
  color: string;
}

const menuItems: MenuItemProps[] = [
  {
    title: 'Ürünler',
    icon: 'list.bullet',
    route: '/(tabs)/products',
    color: '#007AFF',
  },
  {
    title: 'Siparişler',
    icon: 'paperplane.fill',
    route: '/(tabs)/orders',
    color: '#FF9500',
  },
  {
    title: 'Maliyetler',
    icon: 'gear',
    route: '/(tabs)/costs',
    color: '#34C759',
  },
  {
    title: 'Raporlar',
    icon: 'chart.bar',
    route: '/(tabs)/reports',
    color: '#AF52DE',
  },
];

export function HomeMenuGrid() {
  const [showUserProfile, setShowUserProfile] = useState(false);
  const { user } = useAuth();
  const backgroundColor = useThemeColor({}, 'background');
  const surfaceColor = useThemeColor({}, 'surface');
  const textColor = useThemeColor({}, 'text');
  const borderColor = useThemeColor({ light: '#E5E5E7', dark: '#38383A' }, 'text');

  const handleMenuPress = (route: string) => {
    router.push(route as any);
  };



  const renderMenuItem = (item: MenuItemProps, index: number) => (
    <TouchableOpacity
      key={index}
      style={[
        styles.menuItem,
        {
          backgroundColor: surfaceColor,
          shadowColor: textColor,
        }
      ]}
      onPress={() => handleMenuPress(item.route)}
      activeOpacity={0.8}
    >
      <ThemedView style={[styles.iconContainer, { backgroundColor: item.color }]}>
        <IconSymbol
          name={item.icon as any}
          size={28}
          color="#FFFFFF"
        />
      </ThemedView>
      <ThemedText style={[styles.menuTitle, { color: textColor }]}>
        {item.title}
      </ThemedText>
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerTop}>
          <ThemedView style={styles.headerContent}>
            <ThemedText type="title" style={[styles.title, { color: textColor }]}>
              Stok Yönetimi
            </ThemedText>
            <ThemedText style={[styles.subtitle, { color: textColor }]}>
              {user?.fullName ? `Hoş geldiniz, ${user.fullName}!` : 'Hoş geldiniz!'}
            </ThemedText>
          </ThemedView>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => setShowUserProfile(true)}
          >
            <ThemedText style={styles.profileIcon}>
              {user?.role === 'admin' ? '👑' :
               user?.role === 'manager' ? '👔' :
               user?.role === 'employee' ? '👨‍💼' : '👁️'}
            </ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.gridContainer}>
        {menuItems.map((item, index) => renderMenuItem(item, index))}
      </ThemedView>

      <ThemedView style={styles.footer}>
        <ThemedText style={styles.footerText}>
          Tüm işlemlerinizi buradan yönetebilirsiniz
        </ThemedText>
      </ThemedView>

      {/* User Profile Modal */}
      <Modal
        visible={showUserProfile}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowUserProfile(false)}
      >
        <UserProfile onClose={() => setShowUserProfile(false)} />
      </Modal>
    </ThemedView>
  );
}

const { width } = Dimensions.get('window');
const itemWidth = (width - 80) / 2; // 2 items per row with more padding

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 32,
    paddingTop: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
  },

  profileButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'rgba(0, 122, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  profileIcon: {
    fontSize: 24,
  },
  title: {
    marginBottom: 8,
    textAlign: 'left',
    fontSize: 28,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 17,
    opacity: 0.7,
    textAlign: 'left',
    letterSpacing: -0.2,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 20,
    paddingHorizontal: 4,
  },
  menuItem: {
    width: itemWidth,
    height: itemWidth,
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 0,
    letterSpacing: -0.2,
  },
  footer: {
    marginTop: 40,
    alignItems: 'center',
    paddingBottom: 20,
  },
  footerText: {
    fontSize: 14,
    opacity: 0.6,
    textAlign: 'center',
  },
});
