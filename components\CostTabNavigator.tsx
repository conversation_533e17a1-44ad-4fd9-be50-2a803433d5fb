import { useSupabaseCosts } from '@/hooks/useSupabaseCosts';
import { useSupabaseProducts } from '@/hooks/useSupabaseProducts';
import { useCallback, useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { CostForm } from './CostForm';
import { CostList } from './CostList';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

type Tab = 'add' | 'table';

export function CostTabNavigator() {
  const [activeTab, setActiveTab] = useState<Tab>('add');
  const { costs, loading, addCost } = useSupabaseCosts();
  const { products, loading: productsLoading } = useSupabaseProducts();

  const handleAddCost = useCallback(async (
    productName: string,
    quantity: number,
    essencePrice: number,
    essenceAmount: number,
    essenceUsed: number,
    alcoholPrice: number,
    alcoholUsed: number,
    bottlePrice: number
  ) => {
    try {
      await addCost({
        product_name: productName,
        quantity,
        essence_price: essencePrice,
        essence_amount: essenceAmount,
        essence_used: essenceUsed,
        alcohol_price: alcoholPrice,
        alcohol_used: alcoholUsed,
        bottle_price: bottlePrice
      });
    } catch (error) {
      console.error('Error adding cost:', error);
      throw error;
    }
  }, [addCost]);

  const renderTabButton = (tab: Tab, label: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tab && styles.activeTabButton
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <ThemedText
        style={[
          styles.tabButtonText,
          activeTab === tab && styles.activeTabButtonText
        ]}
      >
        {label}
      </ThemedText>
    </TouchableOpacity>
  );

  const renderContent = () => {
    if (loading || productsLoading) {
      return (
        <ThemedView style={styles.loadingContainer}>
          <ThemedText>Yükleniyor...</ThemedText>
        </ThemedView>
      );
    }

    switch (activeTab) {
      case 'add':
        return (
          <CostForm
            key={products.length}
            onAddCost={handleAddCost}
            products={products}
          />
        );
      case 'table':
        return (
          <CostList
            costs={costs}
            onDeleteCost={async () => {}} // Placeholder function
          />
        );
      default:
        return null;
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.title}>
          Maliyet Yönetimi
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.tabContainer}>
        {renderTabButton('add', 'Maliyet Ekleme')}
        {renderTabButton('table', 'Maliyet Tablosu')}
      </ThemedView>

      <ThemedView style={styles.content}>
        {renderContent()}
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tabButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabButtonText: {
    fontSize: 16,
  },
  activeTabButtonText: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
