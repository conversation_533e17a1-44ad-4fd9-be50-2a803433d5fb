import * as Updates from 'expo-updates';
import { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

export const UpdateDebugInfo = () => {
  const [updateInfo, setUpdateInfo] = useState<{
    runtimeVersion: string | null;
    updateId: string | null;
    createdAt: string | null;
  }>({
    runtimeVersion: null,
    updateId: null,
    createdAt: null,
  });

  useEffect(() => {
    const getUpdateInfo = async () => {
      try {
        const update = await Updates.checkForUpdateAsync();
        setUpdateInfo({
          runtimeVersion: Updates.runtimeVersion,
          updateId: update.isAvailable ? update.manifest?.id || null : null,
          createdAt: update.isAvailable ? (update.manifest?.extra as any)?.createdAt || null : null,
        });
      } catch (error) {
        console.error('Error checking for updates:', error);
      }
    };

    getUpdateInfo();
  }, []);

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        EAS Update Info
      </ThemedText>
      <View style={styles.infoContainer}>
        <ThemedText style={styles.label}>Runtime Version:</ThemedText>
        <ThemedText style={styles.value}>{updateInfo.runtimeVersion || 'N/A'}</ThemedText>
        
        <ThemedText style={styles.label}>Update ID:</ThemedText>
        <ThemedText style={styles.value}>{updateInfo.updateId || 'N/A'}</ThemedText>
        
        <ThemedText style={styles.label}>Created At:</ThemedText>
        <ThemedText style={styles.value}>
          {updateInfo.createdAt ? new Date(updateInfo.createdAt).toLocaleString() : 'N/A'}
        </ThemedText>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  title: {
    marginBottom: 12,
  },
  infoContainer: {
    gap: 8,
  },
  label: {
    fontSize: 14,
    opacity: 0.7,
  },
  value: {
    fontSize: 14,
    marginBottom: 8,
  },
}); 