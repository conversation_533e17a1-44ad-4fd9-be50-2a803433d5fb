import { ProductItemSkeleton } from '@/components/LoadingSkeleton';
import { ProductItem } from '@/components/ProductItem';
import { SwipeableRow } from '@/components/SwipeableRow';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Product } from '@/types/Product';
import React from 'react';
import { FlatList, RefreshControl, StyleSheet } from 'react-native';

interface ProductListProps {
  products: Product[];
  onDeleteProduct: (productId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  refreshing?: boolean;
  scrollEnabled?: boolean;
}

export function ProductList({
  products = [],
  onDeleteProduct,
  onRefresh,
  refreshing = false,
  scrollEnabled = true
}: ProductListProps) {

  const renderProduct = ({ item }: { item: Product }) => {
    if (!item || !item.id) {
      console.warn('Invalid product item:', item);
      return null;
    }

    return (
      <SwipeableRow
        rightActions={[
          {
            text: 'Sil',
            backgroundColor: '#FF3B30',
            icon: '🗑️',
            onPress: () => onDeleteProduct(item.id),
          },
        ]}
      >
        <ProductItem
          product={item}
          onDelete={onDeleteProduct}
        />
      </SwipeableRow>
    );
  };

  // Show skeleton loading when refreshing
  if (refreshing && (!products || products.length === 0)) {
    return (
      <ThemedView style={styles.container}>
        {[...Array(3)].map((_, index) => (
          <ProductItemSkeleton key={index} />
        ))}
      </ThemedView>
    );
  }

  const renderEmptyComponent = () => (
    <ThemedView style={styles.emptyContainer}>
      <ThemedText style={styles.emptyText}>
        Henüz ürün eklenmemiş
      </ThemedText>
      <ThemedText style={styles.emptySubtext}>
        Yukarıdaki formu kullanarak ilk ürününüzü ekleyin
      </ThemedText>
    </ThemedView>
  );

  const renderHeader = () => (
    <ThemedText type="subtitle" style={styles.title}>
      Ürün Listesi ({products?.length || 0})
    </ThemedText>
  );

  // Ensure products is an array
  const safeProducts = Array.isArray(products) ? products : [];

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={safeProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item?.id || Math.random().toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyComponent}
        refreshControl={
          scrollEnabled ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          ) : undefined
        }
        scrollEnabled={scrollEnabled}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={safeProducts.length === 0 ? styles.emptyList : styles.listContent}
        style={styles.flatList}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    marginBottom: -30, // Android: Prevent bottom overlay
  },
  flatList: {
    flex: 1, // ENSURE FLATLIST TAKES FULL HEIGHT
  },
  title: {
    marginBottom: 15,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    minHeight: 200, // MINIMUM HEIGHT FOR EMPTY STATE
  },
  emptyText: {
    fontSize: 18,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  emptyList: {
    flexGrow: 1, // GROW TO FILL AVAILABLE SPACE
    justifyContent: 'center', // CENTER EMPTY CONTENT
  },
  listContent: {
    paddingBottom: 0, // Android: Remove bottom padding
    flexGrow: 1,
  },
});
