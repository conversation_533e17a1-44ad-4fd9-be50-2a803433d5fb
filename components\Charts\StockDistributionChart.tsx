import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Product } from '@/types/Product';
import { Dimensions, StyleSheet } from 'react-native';
import { BarChart } from 'react-native-chart-kit';

interface StockDistributionChartProps {
  products: Product[];
}

export function StockDistributionChart({ products }: StockDistributionChartProps) {
  const screenWidth = Dimensions.get('window').width;
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  // Take top 6 products by stock
  const topProducts = products
    .sort((a, b) => b.stock - a.stock)
    .slice(0, 6);

  // Prepare chart data
  const chartData = {
    labels: topProducts.map(product => 
      product.name.length > 8 ? product.name.substring(0, 8) + '...' : product.name
    ),
    datasets: [
      {
        data: topProducts.map(product => product.stock),
        colors: topProducts.map((_, index) => {
          const colors = [
            '#34C759', '#007AFF', '#FF9500', '#FF3B30', '#5856D6', '#FF2D92'
          ];
          return () => colors[index % colors.length];
        }),
      },
    ],
  };

  const chartConfig = {
    backgroundColor: backgroundColor,
    backgroundGradientFrom: backgroundColor,
    backgroundGradientTo: backgroundColor,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
    labelColor: (opacity = 1) => textColor,
    style: {
      borderRadius: 16,
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: `rgba(0, 122, 255, 0.2)`,
      strokeWidth: 1,
    },
  };

  if (products.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText type="subtitle" style={styles.title}>
          📊 Stok Dağılımı
        </ThemedText>
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyText}>
            Henüz ürün bulunmuyor
          </ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="subtitle" style={styles.title}>
        📊 Stok Dağılımı (Top 6)
      </ThemedText>
      
      <ThemedView style={styles.chartContainer}>
        <BarChart
          data={chartData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          style={styles.chart}
          withInnerLines={true}
          withVerticalLabels={true}
          withHorizontalLabels={true}
          fromZero={true}
          showBarTops={true}
          showValuesOnTopOfBars={true}
          yAxisLabel=""
          yAxisSuffix=""
        />
      </ThemedView>

      {/* Stock Summary */}
      <ThemedView style={styles.summary}>
        <ThemedView style={styles.summaryItem}>
          <ThemedText style={styles.summaryNumber}>
            {products.reduce((sum, p) => sum + p.stock, 0)}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Toplam Stok</ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.summaryItem}>
          <ThemedText style={styles.summaryNumber}>
            {products.length}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Ürün Çeşidi</ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.summaryItem}>
          <ThemedText style={styles.summaryNumber}>
            {products.length > 0 ? Math.round(products.reduce((sum, p) => sum + p.stock, 0) / products.length) : 0}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Ortalama Stok</ThemedText>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 30,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(52, 199, 89, 0.1)',
  },
  title: {
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 18,
    fontWeight: '700',
  },
  chartContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  chart: {
    borderRadius: 16,
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#34C759',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    opacity: 0.6,
  },
});
