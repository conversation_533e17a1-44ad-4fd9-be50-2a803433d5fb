import { ErrorBoundary } from '@/components/ErrorBoundary';
import { NetworkStatusBanner } from '@/components/NetworkStatusBanner';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { ImmersiveStatusBar, useImmersiveMode } from '@/hooks/useImmersiveMode';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import * as SplashScreenModule from 'expo-splash-screen';
import * as Updates from 'expo-updates';
import { useEffect, useRef, useState } from 'react';
import { Image, View, useColorScheme } from 'react-native';
import { AuthProvider } from '../contexts/AuthContext';
import { useNetworkStatus } from '../hooks/useNetworkStatus';

// Splash ekranını manuel olarak kontrol etmek için
SplashScreen.preventAutoHideAsync();
SplashScreenModule.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(false);
  const updateIntervalRef = useRef<ReturnType<typeof setInterval>>();
  const { isConnected } = useNetworkStatus();

  // Gelişmiş immersive mode hook'u
  useImmersiveMode({
    hideNavigationBar: true,
    hideStatusBar: true,
    autoHideOnFocus: true,
    enableSwipeGesture: true,
  });

  // Initial update check
  useEffect(() => {
    const checkForUpdates = async () => {
      if (!isConnected) return;
      
      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          await Updates.fetchUpdateAsync();
          await Updates.reloadAsync();
        }
      } catch (error) {
        console.error('Error checking for updates:', error);
      }
    };

    checkForUpdates();
  }, [isConnected]);

  // Periodic update check
  useEffect(() => {
    if (!isConnected) return;

    const checkForUpdates = async () => {
      if (isCheckingUpdate) return;
      setIsCheckingUpdate(true);

      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          await Updates.fetchUpdateAsync();
          await Updates.reloadAsync();
        }
      } catch (error) {
        console.error('Error checking for updates:', error);
      } finally {
        setIsCheckingUpdate(false);
      }
    };

    // Initial check
    checkForUpdates();

    // Set up periodic check every 15 minutes
    updateIntervalRef.current = setInterval(checkForUpdates, 15 * 60 * 1000);

    // Cleanup
    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [isConnected, isCheckingUpdate]);

  const [fontsLoaded, fontError] = useFonts({
    'SpaceMono-Regular': require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    // Fontlar yüklendiğinde veya hata oluştuğunda splash ekranını gizle
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
      SplashScreenModule.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (fontError) throw fontError;
  }, [fontError]);

  // Fontlar yüklenene kadar özel splash ekranı göster
  if (!fontsLoaded && !fontError) {
    return (
      <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: '#000000' }}>
        <Image 
          source={require('../assets/images/logo.png')} 
          style={{ width: 200, height: 200, resizeMode: 'contain' }}
        />
      </View>
    );
  }

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthProvider>
          <NetworkStatusBanner />
          <ImmersiveStatusBar useSystemTheme={true} />
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: colorScheme === 'dark' ? '#000' : '#fff',
              },
              headerTintColor: colorScheme === 'dark' ? '#fff' : '#000',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
            }}
          >
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
          </Stack>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}