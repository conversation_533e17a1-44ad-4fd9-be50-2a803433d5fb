import { KPIDashboard } from '@/components/Charts/CircularProgress';
import { ProfitLossChart } from '@/components/Charts/ProfitLossChart';
import { StockDistributionChart } from '@/components/Charts/StockDistributionChart';
import { StatCardSkeleton } from '@/components/LoadingSkeleton';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { UpdateDebugInfo } from '@/components/UpdateDebugInfo';
import { useSupabaseCosts as useCosts } from '@/hooks/useSupabaseCosts';
import { useSupabaseOrders as useOrders } from '@/hooks/useSupabaseOrders';
import { useSupabaseProducts as useProducts } from '@/hooks/useSupabaseProducts';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useEffect } from 'react';
import { Platform, SafeAreaView, ScrollView, StatusBar, StyleSheet } from 'react-native';

export default function ReportsScreen() {
  const { products, loading: productsLoading, refreshProducts } = useProducts();
  const { orders, isLoading: ordersLoading } = useOrders();
  const { getCostSummary } = useCosts();
  const backgroundColor = useThemeColor({}, 'background');

  // Debug: Log products (only in development)
  if (__DEV__) console.log('ReportsScreen - Products:', products.length, products);

  // Refresh products when component mounts
  useEffect(() => {
    refreshProducts();
  }, []);

  const loading = productsLoading || ordersLoading;

  if (loading) {
    return (
      <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
        <ThemedView style={styles.container} noBackground>
          <ThemedView style={styles.header}>
            <ThemedText type="title" style={styles.title}>
              Raporlar
            </ThemedText>
          </ThemedView>
          <ScrollView style={styles.scrollView}>
            <ThemedView style={styles.reportSection}>
              <ThemedText type="subtitle" style={styles.sectionTitle}>
                Ürün İstatistikleri
              </ThemedText>
              <ThemedView style={styles.statsGrid}>
                {[...Array(4)].map((_, index) => (
                  <StatCardSkeleton key={index} />
                ))}
              </ThemedView>
            </ThemedView>

            <ThemedView style={styles.reportSection}>
              <ThemedText type="subtitle" style={styles.sectionTitle}>
                Sipariş Analizi
              </ThemedText>
              <ThemedView style={styles.statsGrid}>
                {[...Array(4)].map((_, index) => (
                  <StatCardSkeleton key={`order-${index}`} />
                ))}
              </ThemedView>
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </SafeAreaView>
    );
  }

  const totalProducts = products.length;
  const totalStock = products.reduce((sum, product) => sum + product.stock, 0);
  const totalOrders = orders.length;
  const completedOrders = orders.filter(order => order.status === 'completed').length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const totalRevenue = orders
    .filter(order => order.status === 'completed' && order.salePrice)
    .reduce((sum, order) => sum + (order.salePrice || 0), 0);

  const costSummary = getCostSummary();
  const totalProfit = totalRevenue - costSummary.totalCost;

  // Prepare chart data
  const profitLossData = [
    { month: 'Oca', revenue: totalRevenue * 0.7, cost: costSummary.totalCost * 0.6, profit: (totalRevenue * 0.7) - (costSummary.totalCost * 0.6) },
    { month: 'Şub', revenue: totalRevenue * 0.8, cost: costSummary.totalCost * 0.7, profit: (totalRevenue * 0.8) - (costSummary.totalCost * 0.7) },
    { month: 'Mar', revenue: totalRevenue * 0.9, cost: costSummary.totalCost * 0.8, profit: (totalRevenue * 0.9) - (costSummary.totalCost * 0.8) },
    { month: 'Nis', revenue: totalRevenue, cost: costSummary.totalCost, profit: totalProfit },
  ];

  // Prepare KPI data
  const kpiData = [
    {
      title: 'Stok Doluluk',
      value: `${totalStock}`,
      percentage: Math.min((totalStock / (totalProducts * 50)) * 100, 100),
      color: '#34C759',
      subtitle: 'adet'
    },
    {
      title: 'Sipariş Başarı',
      value: `${completedOrders}`,
      percentage: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
      color: '#007AFF',
      subtitle: `${totalOrders} toplam`
    },
    {
      title: 'Kar Marjı',
      value: `${totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(0) : 0}%`,
      percentage: Math.max(0, Math.min(100, totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100) : 0)),
      color: totalProfit >= 0 ? '#34C759' : '#FF3B30',
      subtitle: 'kar oranı'
    },
    {
      title: 'Ürün Çeşitliliği',
      value: `${totalProducts}`,
      percentage: Math.min((totalProducts / 20) * 100, 100),
      color: '#FF9500',
      subtitle: 'çeşit'
    },
  ];

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor }]}>
      <ThemedView style={styles.container} noBackground>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.title}>
            Raporlar
          </ThemedText>
        </ThemedView>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Genel Özet - Moved to top */}
        <ThemedView style={styles.reportSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Genel Özet
          </ThemedText>
          
          <ThemedView style={styles.statsGrid}>
            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{totalProducts}</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Ürün</ThemedText>
            </ThemedView>
            
            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{totalStock}</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Stok</ThemedText>
            </ThemedView>
            
            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{totalOrders}</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Sipariş</ThemedText>
            </ThemedView>
            
            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{completedOrders}</ThemedText>
              <ThemedText style={styles.statLabel}>Tamamlanan</ThemedText>
            </ThemedView>

            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{pendingOrders}</ThemedText>
              <ThemedText style={styles.statLabel}>Bekleyen</ThemedText>
            </ThemedView>

            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{totalRevenue.toFixed(0)} ₺</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Gelir</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.reportSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Maliyet ve Kar Analizi
          </ThemedText>

          <ThemedView style={styles.statsGrid}>
            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{costSummary.totalEssenceUsed.toFixed(0)} ml</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Esans</ThemedText>
            </ThemedView>

            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{costSummary.totalAlcoholUsed.toFixed(0)} ml</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Alkol</ThemedText>
            </ThemedView>

            <ThemedView style={styles.statCard}>
              <ThemedText style={styles.statNumber}>{costSummary.totalCost.toFixed(0)} ₺</ThemedText>
              <ThemedText style={styles.statLabel}>Toplam Maliyet</ThemedText>
            </ThemedView>

            <ThemedView style={[styles.statCard, { backgroundColor: totalProfit >= 0 ? 'rgba(52, 199, 89, 0.1)' : 'rgba(255, 59, 48, 0.1)' }]}>
              <ThemedText style={[styles.statNumber, { color: totalProfit >= 0 ? '#34c759' : '#ff3b30' }]}>
                {totalProfit.toFixed(0)} ₺
              </ThemedText>
              <ThemedText style={styles.statLabel}>Net Kar/Zarar</ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        <ThemedView style={styles.reportSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Ürün Detayları
          </ThemedText>
          
          {products.map(product => (
            <ThemedView key={product.id} style={styles.productDetail}>
              <ThemedText style={styles.productName}>{product.name}</ThemedText>
              <ThemedText style={styles.productStock}>Stok: {product.stock} adet</ThemedText>
            </ThemedView>
          ))}
        </ThemedView>

        {/* Charts Section - Moved below Genel Özet */}
        <ThemedView style={styles.reportSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Analitik Grafikler
          </ThemedText>
        </ThemedView>

        {/* KPI Dashboard */}
        <KPIDashboard kpis={kpiData} />

        {/* Profit/Loss Chart */}
        <ProfitLossChart data={profitLossData} />

        {/* Stock Distribution Chart */}
        <StockDistributionChart products={products} />

        {/* EAS Update Debug Info */}
        {__DEV__ && <UpdateDebugInfo />}
      </ScrollView>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    // backgroundColor will be set dynamically via useThemeColor
    ...Platform.select({
      android: {
        paddingTop: StatusBar.currentHeight || 0,
        paddingBottom: 0, // Remove bottom padding for Android
        marginBottom: -20, // Prevent bottom overlay
      },
    }),
  },

  container: {
    flex: 1,
    marginBottom: -30, // Android: Prevent bottom black layer
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
    marginBottom: 8,
  },
  title: {
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    padding: 16,
    marginBottom: -40, // Android: Prevent bottom overlay
  },
  scrollContent: {
    paddingBottom: 0, // Android: Remove bottom padding
    flexGrow: 1,
  },
  reportSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 15,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 10,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 20,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    fontWeight: '500',
    letterSpacing: -0.2,
  },
  productDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  productStock: {
    fontSize: 14,
    opacity: 0.7,
    fontWeight: '500',
    letterSpacing: -0.1,
  },
});
