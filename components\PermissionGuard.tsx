import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/hooks/useAuth';
import { UserPermissions } from '@/types/User';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: keyof UserPermissions;
  fallback?: React.ReactNode;
  showMessage?: boolean;
}

export function PermissionGuard({ 
  children, 
  permission, 
  fallback,
  showMessage = true 
}: PermissionGuardProps) {
  const { hasPermission, user } = useAuth();

  if (!hasPermission(permission)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (!showMessage) {
      return null;
    }

    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.messageContainer}>
          <ThemedText style={styles.icon}>🔒</ThemedText>
          <ThemedText style={styles.title}><PERSON><PERSON><PERSON><PERSON></ThemedText>
          <ThemedText style={styles.message}>
            Bu işlem için yetkiniz bulunmuyor.
          </ThemedText>
          <ThemedText style={styles.roleInfo}>
            Mevcut rolünüz: {user?.role === 'admin' ? '👑 Yönetici' : 
                            user?.role === 'manager' ? '👔 Müdür' :
                            user?.role === 'employee' ? '👨‍💼 Çalışan' : '👁️ Görüntüleyici'}
          </ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return <>{children}</>;
}

// Specific permission guards for common use cases
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canManageUsers" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function ManagerOrAbove({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canViewFinancials" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanAddProducts({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canAddProducts" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanDeleteProducts({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canDeleteProducts" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanCreateOrders({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canCreateOrders" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanUpdateOrderStatus({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canUpdateOrderStatus" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanViewCosts({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canViewCosts" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanAddCosts({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canAddCosts" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanViewReports({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canViewReports" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

export function CanExportReports({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <PermissionGuard permission="canExportReports" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  messageContainer: {
    alignItems: 'center',
    padding: 30,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 59, 48, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.2)',
    maxWidth: 300,
  },
  icon: {
    fontSize: 48,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
    color: '#FF3B30',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
    opacity: 0.8,
  },
  roleInfo: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.6,
    fontStyle: 'italic',
  },
});
