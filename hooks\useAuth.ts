import { AuthState, DEFAULT_PERMISSIONS, LoginCredentials, User, UserPermissions } from '@/types/User';
import { useCallback, useEffect, useState } from 'react';

// Global auth initialization state - prevents multiple initializations
let globalAuthInitialized = false;
let globalAuthState: AuthState | null = null;
let globalAuthListeners: Set<(state: AuthState) => void> = new Set();

// Global auth state management - PREVENT INFINITE LOOPS
const setGlobalAuthState = (newState: AuthState) => {
  // Only update if state actually changed
  if (!globalAuthState ||
      globalAuthState.isAuthenticated !== newState.isAuthenticated ||
      globalAuthState.user?.id !== newState.user?.id ||
      globalAuthState.isLoading !== newState.isLoading) {

    globalAuthState = newState;
    globalAuthListeners.forEach(listener => {
      try {
        listener(newState);
      } catch (error) {
        console.warn('Global auth listener error:', error);
      }
    });
  }
};

const addGlobalAuthListener = (listener: (state: AuthState) => void) => {
  globalAuthListeners.add(listener);
  return () => globalAuthListeners.delete(listener);
};
// Simple and reliable storage system
const storage = {
  data: new Map<string, string>(),

  async getItem(key: string): Promise<string | null> {
    console.log('🔍 Getting item:', key);

    // Try sessionStorage first (more reliable than localStorage)
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const sessionValue = sessionStorage.getItem(key);
        if (sessionValue) {
          console.log('✅ Found in sessionStorage');
          return sessionValue;
        }
      } catch (e) {
        console.log('❌ sessionStorage error:', e);
      }
    }

    // Try localStorage as backup
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const localValue = localStorage.getItem(key);
        if (localValue) {
          console.log('✅ Found in localStorage');
          return localValue;
        }
      } catch (e) {
        console.log('❌ localStorage error:', e);
      }
    }

    // Memory storage as last resort
    const memoryValue = this.data.get(key) || null;
    console.log(memoryValue ? '✅ Found in memory' : '❌ Not found anywhere');
    return memoryValue;
  },

  async setItem(key: string, value: string): Promise<void> {
    console.log('💾 Setting item:', key);

    // Save to sessionStorage (primary)
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        sessionStorage.setItem(key, value);
        console.log('✅ Saved to sessionStorage');
      } catch (e) {
        console.log('❌ sessionStorage save error:', e);
      }
    }

    // Save to localStorage (backup)
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem(key, value);
        console.log('✅ Saved to localStorage');
      } catch (e) {
        console.log('❌ localStorage save error:', e);
      }
    }

    // Always save to memory
    this.data.set(key, value);
    console.log('✅ Saved to memory');
  },

  async removeItem(key: string): Promise<void> {
    console.log('🗑️ Removing item:', key);

    if (typeof window !== 'undefined') {
      try {
        sessionStorage.removeItem(key);
        localStorage.removeItem(key);
      } catch (e) {
        console.log('❌ Storage remove error:', e);
      }
    }

    this.data.delete(key);
    console.log('✅ Removed from all storages');
  }
};

const AUTH_STORAGE_KEY = '@inventory_auth';
const USERS_STORAGE_KEY = '@inventory_users';

// Backend yönetici hesapları - frontend'de görünmez
const BACKEND_ADMIN_ACCOUNTS: User[] = [
  {
    id: '1',
    username: 'Gencer',
    email: '<EMAIL>',
    fullName: 'Ana Yönetici - Gencer',
    role: 'admin',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastLogin: new Date(),
    permissions: DEFAULT_PERMISSIONS.admin,
  },
  {
    id: '2',
    username: 'Kurt',
    email: '<EMAIL>',
    fullName: 'Yönetici - Kurt',
    role: 'admin',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    lastLogin: new Date(),
    permissions: DEFAULT_PERMISSIONS.admin,
  },
];

// Backend şifre doğrulama (gerçek uygulamada API çağrısı olacak)
const BACKEND_PASSWORDS: Record<string, string> = {
  'Gencer': 'Gencer103',
  'gencer': 'Gencer103',
  'GENCER': 'Gencer103',
  'Kurt': 'Kurt123',
  'kurt': 'Kurt123',
  'KURT': 'Kurt123',
};

export function useAuth() {
  // Use global auth state if available, otherwise use default
  const [authState, setAuthState] = useState<AuthState>(() => {
    if (globalAuthState) {
      if (__DEV__) console.log('🔄 useAuth using existing global auth state');
      return globalAuthState;
    }
    if (__DEV__) console.log('🔄 useAuth creating new auth state');
    return {
      user: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,
    };
  });

  // Initialize backend yönetici accounts (STABLE - no auth clearing)
  const initializeBackendAccounts = useCallback(async () => {
    try {
      // Only update users storage, never touch auth storage
      await storage.setItem(USERS_STORAGE_KEY, JSON.stringify(BACKEND_ADMIN_ACCOUNTS));
      console.log('✅ Backend yönetici hesapları updated (Gencer & Kurt) - AUTH PRESERVED');
    } catch (error) {
      console.error('❌ Error updating backend yönetici hesapları:', error);
    }
  }, []);

  // Load saved auth state - GLOBAL VERSION (only runs once)
  const loadAuthState = useCallback(async () => {
    // Skip if already initialized globally
    if (globalAuthInitialized) {
      console.log('🔄 Auth already initialized globally - SKIPPING to prevent flash');
      if (globalAuthState) {
        setAuthState(globalAuthState);
      }
      return;
    }

    console.log('🔄 Loading auth state for the FIRST TIME globally...');
    globalAuthInitialized = true;

    // Try to load saved auth FIRST
    try {
      const savedAuth = await storage.getItem(AUTH_STORAGE_KEY);

      if (savedAuth) {
        const user: User = JSON.parse(savedAuth);
        console.log('✅ User loaded from storage:', user.username);

        const newAuthState = {
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        };

        setGlobalAuthState(newAuthState);
        setAuthState(newAuthState);

        console.log('🎉 Auth state set to authenticated - GLOBAL STATE UPDATED!');

        // Initialize backend accounts in background (don't affect auth state)
        initializeBackendAccounts().catch(console.warn);
        return;
      }
    } catch (loadError) {
      console.warn('❌ Auth loading failed:', loadError);
    }

    // Only if no saved auth found, initialize and set to not authenticated
    try {
      await initializeBackendAccounts();
    } catch (initError) {
      console.warn('❌ Backend yönetici hesapları initialization failed:', initError);
    }

    // Default to not authenticated
    console.log('❌ No saved auth found - showing login');
    const newAuthState = {
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    };

    setGlobalAuthState(newAuthState);
    setAuthState(newAuthState);
  }, [initializeBackendAccounts]);

  // Backend yönetici login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    if (__DEV__) console.log('🔐 Login attempt for:', credentials.username);

    try {
      // Set loading state
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API delay (shorter for mobile)
      await new Promise(resolve => setTimeout(resolve, 500));

      // Backend yönetici authentication - validate credentials
      if (__DEV__) {
        console.log('🔍 Available users:', BACKEND_ADMIN_ACCOUNTS.map(u => u.username));
        console.log('🔍 Looking for username:', credentials.username);
      }

      // Case-insensitive username search
      const user = BACKEND_ADMIN_ACCOUNTS.find(u =>
        u.username.toLowerCase() === credentials.username.toLowerCase()
      );
      const password = BACKEND_PASSWORDS[credentials.username] ||
                      BACKEND_PASSWORDS[credentials.username.toLowerCase()] ||
                      BACKEND_PASSWORDS[credentials.username.charAt(0).toUpperCase() + credentials.username.slice(1)];

      if (__DEV__) {
        console.log('🔐 User found:', !!user);
        console.log('🔐 Password match:', password === credentials.password);
      }

      if (!user) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Kullanıcı bulunamadı',
        }));
        return false;
      }

      if (!password) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Şifre bulunamadı',
        }));
        return false;
      }

      if (password !== credentials.password) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Şifre hatalı',
        }));
        return false;
      }

      if (!user.isActive) {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Hesabınız devre dışı bırakılmış',
        }));
        return false;
      }

      // Update last login
      const updatedUser = { ...user, lastLogin: new Date() };
      console.log('✅ User validated:', updatedUser.username);

      // Save auth state
      try {
        await storage.setItem(AUTH_STORAGE_KEY, JSON.stringify(updatedUser));
        console.log('✅ Auth saved to storage');
      } catch (storageError) {
        console.warn('❌ Storage save failed:', storageError);
      }

      // Set auth state for backend yönetici authentication - IMMEDIATE UPDATE
      console.log('🎯 Setting yönetici auth state IMMEDIATELY...');
      const newAuthState = {
        user: updatedUser,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

      console.log('🔄 New auth state to be set IMMEDIATELY:', newAuthState);

      // Update both global and local state IMMEDIATELY
      setGlobalAuthState(newAuthState);
      setAuthState(newAuthState);

      // Force immediate state propagation
      console.log('🎉 Yönetici authentication successful! Global auth state updated IMMEDIATELY - NO FLASH!');

      // Trigger immediate re-render for all listeners
      globalAuthListeners.forEach(listener => {
        try {
          listener(newAuthState);
        } catch (error) {
          console.warn('Auth listener error:', error);
        }
      });

      return true;
    } catch (error) {
      console.error('❌ Login error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false, // CRITICAL: Set to false on error too
        error: 'Giriş yapılırken hata oluştu',
      }));
      return false;
    }
  }, []);

  // Logout function - SIMPLIFIED
  const logout = useCallback(async () => {
    try {
      console.log('🚪 Simple logout initiated...');

      // Clear storage immediately
      await storage.removeItem(AUTH_STORAGE_KEY);

      // Clear web storage
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem(AUTH_STORAGE_KEY);
          sessionStorage.removeItem(AUTH_STORAGE_KEY);
        } catch (e) {
          console.warn('Web storage cleanup failed');
        }
      }

      // Set final auth state - NO TIMEOUTS
      const logoutState = {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

      // Update both global and local state
      setGlobalAuthState(logoutState);
      setAuthState(logoutState);

      console.log('✅ Simple logout completed - global auth state reset');

      // Don't force reload on mobile - just log completion
      console.log('✅ Logout completed successfully');

    } catch (error) {
      console.error('❌ Logout error:', error);
      // Don't force reload on mobile - just log error
      console.log('❌ Logout failed - staying on current screen');
    }
  }, []);

  // Check permission
  const hasPermission = useCallback((permission: keyof UserPermissions): boolean => {
    if (!authState.user) return false;
    return authState.user.permissions[permission];
  }, [authState.user]);

  // Get all users (admin only)
  const getAllUsers = useCallback(async (): Promise<User[]> => {
    try {
      if (!hasPermission('canManageUsers')) {
        throw new Error('Bu işlem için yetkiniz yok');
      }

      const usersData = await storage.getItem(USERS_STORAGE_KEY);
      return usersData ? JSON.parse(usersData) : [];
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }, [hasPermission]);

  // Global auth listener for state sync - PREVENT INFINITE LOOPS
  useEffect(() => {
    const unsubscribe = addGlobalAuthListener((newState) => {
      console.log('🔄 useAuth received global auth state update:', newState);
      // Only update if state actually changed to prevent infinite loops
      setAuthState(currentState => {
        if (currentState.isAuthenticated !== newState.isAuthenticated ||
            currentState.user?.id !== newState.user?.id ||
            currentState.isLoading !== newState.isLoading) {
          return newState;
        }
        return currentState; // No change, prevent re-render
      });
    });

    return unsubscribe;
  }, []);

  // Initialize auth state only once globally - PREVENT INFINITE LOOPS
  useEffect(() => {
    if (!globalAuthInitialized) {
      console.log('🔄 useAuth initializing GLOBALLY for the first time - NO FLASH MODE');
      loadAuthState();
    } else {
      console.log('🔄 useAuth using existing global state - SKIPPING initialization to prevent flash');
      if (globalAuthState) {
        // Only update if different to prevent infinite loops
        setAuthState(currentState => {
          if (currentState.isAuthenticated !== globalAuthState.isAuthenticated ||
              currentState.user?.id !== globalAuthState.user?.id) {
            return globalAuthState;
          }
          return currentState;
        });
      }
    }
  }, []); // Empty dependency array - only run once on mount

  // Debug auth state changes (only in development)
  useEffect(() => {
    if (__DEV__) console.log('🔄 useAuth state changed:', authState);
  }, [authState]);

  return {
    ...authState,
    login,
    logout,
    hasPermission,
    getAllUsers,
    refreshAuth: loadAuthState,
  };
}
