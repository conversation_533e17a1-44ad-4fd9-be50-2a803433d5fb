import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme as useRNColorScheme } from 'react-native';

type ColorScheme = 'light' | 'dark';

interface ThemeContextType {
  colorScheme: ColorScheme;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setTheme: (theme: ColorScheme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@inventory_theme_preference';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useRNColorScheme();
  const [colorScheme, setColorScheme] = useState<ColorScheme>('light');
  const [isLoaded, setIsLoaded] = useState(false);

  // Load saved theme preference on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
          console.log('🎨 Loaded saved theme preference:', savedTheme);
          setColorScheme(savedTheme);
        } else {
          // Use system preference if no saved preference
          const defaultTheme = systemColorScheme || 'light';
          console.log('🎨 Using system theme preference:', defaultTheme);
          setColorScheme(defaultTheme);
        }
      } catch (error) {
        console.error('❌ Error loading theme preference:', error);
        setColorScheme('light'); // Fallback to light theme
      } finally {
        setIsLoaded(true);
      }
    };

    loadThemePreference();
  }, [systemColorScheme]);

  // Save theme preference when it changes
  const saveThemePreference = async (theme: ColorScheme) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, theme);
      console.log('💾 Saved theme preference:', theme);
    } catch (error) {
      console.error('❌ Error saving theme preference:', error);
    }
  };

  const setTheme = (theme: ColorScheme) => {
    console.log('🎨 Theme changed to:', theme);
    setColorScheme(theme);
    saveThemePreference(theme);

    // Update web document theme for better web support
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      document.documentElement.style.colorScheme = theme;
      document.documentElement.setAttribute('data-theme', theme);
    }
  };

  const toggleTheme = () => {
    const newTheme = colorScheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const value: ThemeContextType = {
    colorScheme,
    isDarkMode: colorScheme === 'dark',
    toggleTheme,
    setTheme,
  };

  // Don't render children until theme is loaded to prevent flash
  if (!isLoaded) {
    return null;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Enhanced useColorScheme hook that uses our theme context
export function useColorScheme(): ColorScheme {
  const context = useContext(ThemeContext);
  if (context) {
    return context.colorScheme;
  }
  
  // Fallback to system color scheme if context is not available
  const systemColorScheme = useRNColorScheme();
  return systemColorScheme || 'light';
}
