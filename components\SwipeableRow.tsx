import React, { useRef } from 'react';
import { Animated, Dimensions, PanResponder, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const { width: screenWidth } = Dimensions.get('window');
const SWIPE_THRESHOLD = screenWidth * 0.25; // 25% of screen width

interface SwipeAction {
  text: string;
  backgroundColor: string;
  textColor?: string;
  onPress: () => void;
  icon?: string;
}

interface SwipeableRowProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
}

export function SwipeableRow({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeLeft,
  onSwipeRight,
}: SwipeableRowProps) {
  const translateX = useRef(new Animated.Value(0)).current;
  const lastOffset = useRef(0);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 10;
      },
      onPanResponderGrant: () => {
        translateX.setOffset(lastOffset.current);
      },
      onPanResponderMove: Animated.event(
        [null, { dx: translateX }],
        { useNativeDriver: false }
      ),
      onPanResponderRelease: (evt, gestureState) => {
        translateX.flattenOffset();
        const { dx } = gestureState;

        if (dx > SWIPE_THRESHOLD) {
          // Swipe right
          if (leftActions.length > 0) {
            showLeftActions();
          } else if (onSwipeRight) {
            onSwipeRight();
            resetPosition();
          }
        } else if (dx < -SWIPE_THRESHOLD) {
          // Swipe left
          if (rightActions.length > 0) {
            showRightActions();
          } else if (onSwipeLeft) {
            onSwipeLeft();
            resetPosition();
          }
        } else {
          resetPosition();
        }
      },
    })
  ).current;

  const showLeftActions = () => {
    const actionWidth = leftActions.length * 80;
    Animated.spring(translateX, {
      toValue: actionWidth,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    lastOffset.current = actionWidth;
  };

  const showRightActions = () => {
    const actionWidth = rightActions.length * 80;
    Animated.spring(translateX, {
      toValue: -actionWidth,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    lastOffset.current = -actionWidth;
  };

  const resetPosition = () => {
    Animated.spring(translateX, {
      toValue: 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    lastOffset.current = 0;
  };

  const executeAction = (action: SwipeAction) => {
    action.onPress();
    resetPosition();
  };

  const renderActions = (actions: SwipeAction[], isLeft: boolean) => {
    return actions.map((action, index) => (
      <TouchableOpacity
        key={index}
        style={[
          styles.actionButton,
          {
            backgroundColor: action.backgroundColor,
            [isLeft ? 'left' : 'right']: index * 80,
          },
        ]}
        onPress={() => executeAction(action)}
      >
        {action.icon && (
          <Text style={[styles.actionIcon, { color: action.textColor || '#fff' }]}>
            {action.icon}
          </Text>
        )}
        <Text style={[styles.actionText, { color: action.textColor || '#fff' }]}>
          {action.text}
        </Text>
      </TouchableOpacity>
    ));
  };

  return (
    <View style={styles.container}>
      {/* Left Actions */}
      {leftActions.length > 0 && (
        <View style={[styles.actionsContainer, styles.leftActions]}>
          {renderActions(leftActions, true)}
        </View>
      )}

      {/* Right Actions */}
      {rightActions.length > 0 && (
        <View style={[styles.actionsContainer, styles.rightActions]}>
          {renderActions(rightActions, false)}
        </View>
      )}

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            transform: [{ translateX }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  content: {
    backgroundColor: 'transparent',
  },
  actionsContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftActions: {
    left: 0,
  },
  rightActions: {
    right: 0,
  },
  actionButton: {
    width: 80,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  actionIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
