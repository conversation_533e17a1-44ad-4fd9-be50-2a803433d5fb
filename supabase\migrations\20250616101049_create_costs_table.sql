-- Create costs table
CREATE TABLE IF NOT EXISTS costs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_name TEXT NOT NULL,
  quantity INTEGER NOT NULL,
  essence_price DECIMAL(10, 2) NOT NULL,
  essence_amount DECIMAL(10, 2) NOT NULL,
  essence_used DECIMAL(10, 2) NOT NULL,
  alcohol_price DECIMAL(10, 2) NOT NULL,
  alcohol_used DECIMAL(10, 2) NOT NULL,
  bottle_price DECIMAL(10, 2) NOT NULL,
  essence_cost DECIMAL(10, 2) NOT NULL,
  alcohol_cost DECIMAL(10, 2) NOT NULL,
  unit_cost DECIMAL(10, 2) NOT NULL,
  total_cost DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add RLS policies
ALTER TABLE costs ENABLE ROW LEVEL SECURITY;

-- Allow all operations for authenticated users
CREATE POLICY "Allow all operations for authenticated users" ON costs
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);
