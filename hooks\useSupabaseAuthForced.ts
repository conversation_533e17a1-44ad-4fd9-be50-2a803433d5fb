import { supabase } from '@/lib/supabase';
import { AuthState, DEFAULT_PERMISSIONS, LoginCredentials, User } from '@/types/User';
import { useCallback, useEffect, useState } from 'react';

export const useSupabaseAuthForced = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true, // Start with true to check existing session
    error: null,
  });

  // Initialize auth state - CHECK EXISTING SESSION FIRST
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔄 [SUPABASE] Checking for existing session...');

        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ [SUPABASE] Session check error:', error);
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          return;
        }

        if (session?.user) {
          console.log('✅ [SUPABASE] Found existing session for:', session.user.email);

          // Create user object from session - DYNAMIC
          const userEmail = session.user.email || '';
          const user: User = {
            id: session.user.id,
            username: 'Yönetici',
            email: userEmail,
            fullName: userEmail,
            role: 'admin',
            isActive: true,
            createdAt: new Date(session.user.created_at || Date.now()),
            lastLogin: new Date(),
            permissions: DEFAULT_PERMISSIONS.admin,
          };

          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else {
          console.log('ℹ️ [SUPABASE] No existing session found - LOGIN REQUIRED');
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error('❌ [SUPABASE] Auth initialization error:', error);
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    };

    initializeAuth();
  }, []);

  // Listen for auth state changes - PREVENT AUTOMATIC LOGIN ATTEMPTS
  useEffect(() => {
    console.log('🔄 [SUPABASE] Setting up auth state listener...');

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 [SUPABASE] Auth state change event:', event, session?.user?.email || 'no user');

        // Handle different auth events
        if (event === 'SIGNED_IN' && session?.user) {
          console.log('✅ [SUPABASE] User signed in via listener:', session.user.email);

          // Create user object from session - DYNAMIC
          const userEmail = session.user.email || '';
          const user: User = {
            id: session.user.id,
            username: 'Yönetici',
            email: userEmail,
            fullName: userEmail,
            role: 'admin',
            isActive: true,
            createdAt: new Date(session.user.created_at || Date.now()),
            lastLogin: new Date(),
            permissions: DEFAULT_PERMISSIONS.admin,
          };

          setAuthState({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else if (event === 'SIGNED_OUT' || !session) {
          console.log('ℹ️ [SUPABASE] User signed out via listener');
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          console.log('🔄 [SUPABASE] Token refreshed for:', session.user.email);
          // Don't change auth state for token refresh, just log it
        } else {
          console.log('ℹ️ [SUPABASE] Unhandled auth event:', event);
        }
      }
    );

    return () => {
      console.log('🔄 [SUPABASE] Cleaning up auth state listener');
      subscription.unsubscribe();
    };
  }, []);

  // FORCEFUL SUPABASE EMAIL/PASSWORD LOGIN
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      console.log('🚀 [SUPABASE] FORCEFUL LOGIN for:', credentials.username);
      
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Email mapping with support for direct email input
      const emailMap: Record<string, string> = {
        'Gencer': '<EMAIL>',
        'Kurt': '<EMAIL>',
        'gencer': '<EMAIL>',
        'kurt': '<EMAIL>'
      };

      // Check if input is already an email or needs mapping
      let email: string;
      if (credentials.username.includes('@')) {
        // If it's already an email, use it directly
        email = credentials.username.trim().toLowerCase();
        console.log('✅ [SUPABASE] Using direct email:', email);
      } else {
        // Map username to email
        email = emailMap[credentials.username];
        console.log('🔄 [SUPABASE] Mapped username to email:', { username: credentials.username, email });
      }

      if (!email) {
        console.log('❌ [SUPABASE] No email found for input:', credentials.username);
        console.log('❌ [SUPABASE] Available mappings:', Object.keys(emailMap));
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Invalid credentials',
        }));
        return false;
      }

      console.log('🔄 [SUPABASE] Attempting signInWithPassword:', { email });

      // DIRECT SUPABASE AUTH - NO OVERRIDE
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password: credentials.password,
      });

      // ONLY SHOW ERROR IF SUPABASE EXPLICITLY RETURNS ERROR
      if (error) {
        console.error('❌ [SUPABASE] Auth error:', error.message);
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Invalid credentials',
        }));
        return false;
      }

      // SUCCESS: User authenticated via Supabase
      if (data.user && data.session) {
        console.log('✅ [SUPABASE] Login successful! User ID:', data.user.id);
        console.log('✅ [SUPABASE] Session established');

        // Get user metadata from Supabase - DYNAMIC
        const userEmail = data.user.email || '';

        const user: User = {
          id: data.user.id,
          username: 'Yönetici',
          email: userEmail,
          fullName: userEmail,
          role: 'admin',
          isActive: true,
          createdAt: new Date(data.user.created_at || Date.now()),
          lastLogin: new Date(),
          permissions: DEFAULT_PERMISSIONS.admin,
        };

        // FORCE UPDATE AUTH STATE - BIND SESSION
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        console.log('✅ [SUPABASE] User session bound to app context successfully');
        return true;
      }

      console.log('❌ [SUPABASE] No user or session in response');
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Invalid credentials',
      }));
      return false;
    } catch (error) {
      console.error('❌ [SUPABASE] Login exception:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Invalid credentials',
      }));
      return false;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      console.log('🔄 [SUPABASE] Starting logout process...');

      // 1. Call supabase.auth.signOut() - Clear Supabase session
      console.log('🔄 [SUPABASE] Calling supabase.auth.signOut()...');
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('❌ [SUPABASE] Logout error:', error);
        // Continue with logout even if Supabase signOut fails
      } else {
        console.log('✅ [SUPABASE] Supabase session cleared successfully');
      }

      // 2. Clear user session from app context/state
      console.log('🔄 [SUPABASE] Clearing app context and state...');
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      console.log('✅ [SUPABASE] App context cleared - user session removed');

      // 3. Navigate directly to login route - NO PROTECTED SCREENS
      console.log('🔄 [SUPABASE] Navigating directly to login route...');

      try {
        const router = require('expo-router').router;

        // Navigate directly to login route - NOT to tabs or protected screens
        router.replace('/login');
        console.log('✅ [SUPABASE] Navigation to login route successful');

      } catch (navError) {
        console.error('❌ [SUPABASE] Navigation to login failed:', navError);

        // Fallback navigation attempt
        try {
          const router = require('expo-router').router;
          router.push('/login');
          console.log('✅ [SUPABASE] Fallback navigation to login successful');
        } catch (fallbackError) {
          console.error('❌ [SUPABASE] All navigation attempts failed:', fallbackError);
        }
      }

      console.log('✅ [SUPABASE] Logout process completed successfully');

    } catch (error) {
      console.error('❌ [SUPABASE] Logout process failed with exception:', error);

      // Emergency cleanup - Still clear auth state even if logout fails
      console.log('🔄 [SUPABASE] Emergency cleanup - clearing auth state...');
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });

      // Emergency navigation to login
      console.log('🔄 [SUPABASE] Emergency navigation to login...');
      try {
        const router = require('expo-router').router;
        router.replace('/login');
        console.log('✅ [SUPABASE] Emergency navigation to login successful');
      } catch (navError) {
        console.error('❌ [SUPABASE] Emergency navigation failed:', navError);
      }
    }
  }, []);

  const handleSignIn = async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // TODO: Move these email checks to environment variables or a secure configuration
      // WARNING: Hardcoded email checks are not secure and should be moved to a secure configuration
      /*
      if (email === '<EMAIL>') {
        // Admin kullanıcısı için özel işlemler
        console.log('Admin girişi tespit edildi');
      } else if (email === '<EMAIL>') {
        // Test kullanıcısı için özel işlemler
        console.log('Test kullanıcısı girişi tespit edildi');
      }
      */

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data?.user) {
        const userEmail = data.user.email || '';
        const user: User = {
          id: data.user.id,
          username: 'Yönetici',
          email: userEmail,
          fullName: userEmail,
          role: 'admin',
          isActive: true,
          createdAt: new Date(data.user.created_at || Date.now()),
          lastLogin: new Date(),
          permissions: DEFAULT_PERMISSIONS.admin,
        };

        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Giriş yapılırken bir hata oluştu',
      }));
      throw error;
    }
  };

  return {
    ...authState,
    login,
    logout,
    handleSignIn,
  };
};
