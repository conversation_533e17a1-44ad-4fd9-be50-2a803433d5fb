/**
 * New Orders Hook with Backend Integration
 * Handles order CRUD operations with backend synchronization
 */

import { Order } from '@/types/Order';
import { ordersApi, CreateOrderRequest, UpdateOrderStatusRequest } from '@/services/ordersApi';
import { ApiError } from '@/services/api';
import { useCallback, useEffect, useState } from 'react';

// Global orders state for real-time sync
let globalOrders: Order[] = [];
let globalOrdersListeners: Set<(orders: Order[]) => void> = new Set();

// Global orders state management
const setGlobalOrders = (orders: Order[]) => {
  globalOrders = orders;
  globalOrdersListeners.forEach(listener => {
    try {
      listener(orders);
    } catch (error) {
      if (__DEV__) console.error('Orders listener error:', error);
    }
  });
};

const addGlobalOrdersListener = (listener: (orders: Order[]) => void) => {
  globalOrdersListeners.add(listener);
  return () => globalOrdersListeners.delete(listener);
};

/**
 * Enhanced Orders Hook with Backend Integration
 */
export function useOrdersNew() {
  const [orders, setOrders] = useState<Order[]>(globalOrders);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load orders from backend
   */
  const loadOrders = useCallback(async () => {
    try {
      if (__DEV__) console.log('📋 Loading orders from backend...');
      setLoading(true);
      setError(null);

      const fetchedOrders = await ordersApi.getOrders();
      
      // Sort orders: pending first, then by date (newest first)
      const sortedOrders = fetchedOrders.sort((a, b) => {
        // Completed orders go to bottom
        if (a.status === 'completed' && b.status !== 'completed') return 1;
        if (b.status === 'completed' && a.status !== 'completed') return -1;
        
        // Sort by date (newest first)
        return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
      });

      setGlobalOrders(sortedOrders);
      setOrders(sortedOrders);

      if (__DEV__) console.log(`✅ Loaded ${fetchedOrders.length} orders from backend`);
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to load orders';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Add new order
   */
  const addOrder = useCallback(async (
    productName: string, 
    customerName: string, 
    quantity: number, 
    productId?: string,
    salePrice?: number
  ): Promise<void> => {
    try {
      if (__DEV__) console.log('📋 Adding order for:', customerName);
      setError(null);

      const orderData: CreateOrderRequest = {
        productId,
        productName: productName.trim(),
        customerName: customerName.trim(),
        quantity,
        salePrice,
      };

      const newOrder = await ordersApi.createOrder(orderData);

      // Update global state with sorted orders
      const updatedOrders = [newOrder, ...globalOrders].sort((a, b) => {
        // Completed orders go to bottom
        if (a.status === 'completed' && b.status !== 'completed') return 1;
        if (b.status === 'completed' && a.status !== 'completed') return -1;
        
        // Sort by date (newest first)
        return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
      });

      setGlobalOrders(updatedOrders);
      setOrders(updatedOrders);

      if (__DEV__) console.log('✅ Order added successfully');
    } catch (error) {
      console.error('❌ Error adding order:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to add order';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Update order status
   */
  const updateOrderStatus = useCallback(async (
    orderId: string, 
    status: Order['status'], 
    salePrice?: number
  ): Promise<void> => {
    try {
      if (__DEV__) console.log('📋 Updating order status:', orderId, status);
      setError(null);

      const statusData: UpdateOrderStatusRequest = {
        id: orderId,
        status,
        salePrice,
      };

      const updatedOrder = await ordersApi.updateOrderStatus(statusData);

      // Update global state with sorted orders
      const updatedOrders = globalOrders.map(order =>
        order.id === orderId ? updatedOrder : order
      ).sort((a, b) => {
        // Completed orders go to bottom
        if (a.status === 'completed' && b.status !== 'completed') return 1;
        if (b.status === 'completed' && a.status !== 'completed') return -1;
        
        // Sort by date (newest first)
        return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
      });

      setGlobalOrders(updatedOrders);
      setOrders(updatedOrders);

      if (__DEV__) console.log('✅ Order status updated successfully');
    } catch (error) {
      console.error('❌ Error updating order status:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to update order status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Delete order
   */
  const deleteOrder = useCallback(async (orderId: string): Promise<void> => {
    try {
      if (__DEV__) console.log('📋 Deleting order:', orderId);
      setError(null);

      await ordersApi.deleteOrder(orderId);

      // Update global state
      const updatedOrders = globalOrders.filter(order => order.id !== orderId);
      setGlobalOrders(updatedOrders);
      setOrders(updatedOrders);

      if (__DEV__) console.log('✅ Order deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting order:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to delete order';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  /**
   * Get orders by status
   */
  const getOrdersByStatus = useCallback(async (status: Order['status']): Promise<Order[]> => {
    try {
      if (__DEV__) console.log('📋 Getting orders by status:', status);
      setError(null);

      const statusOrders = await ordersApi.getOrdersByStatus(status);

      if (__DEV__) console.log(`✅ Found ${statusOrders.length} orders with status ${status}`);
      return statusOrders;
    } catch (error) {
      console.error('❌ Error getting orders by status:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to get orders by status';
      setError(errorMessage);
      return [];
    }
  }, []);

  /**
   * Search orders
   */
  const searchOrders = useCallback(async (query: string): Promise<Order[]> => {
    try {
      if (__DEV__) console.log('📋 Searching orders:', query);
      setError(null);

      const searchResults = await ordersApi.searchOrders(query);

      if (__DEV__) console.log(`✅ Found ${searchResults.length} orders`);
      return searchResults;
    } catch (error) {
      console.error('❌ Error searching orders:', error);
      const errorMessage = error instanceof ApiError ? error.message : 'Failed to search orders';
      setError(errorMessage);
      return [];
    }
  }, []);

  /**
   * Refresh orders from backend
   */
  const refreshOrders = useCallback(async (): Promise<void> => {
    await loadOrders();
  }, [loadOrders]);

  /**
   * Get order by ID
   */
  const getOrderById = useCallback((orderId: string): Order | undefined => {
    return orders.find(order => order.id === orderId);
  }, [orders]);

  /**
   * Get pending orders count
   */
  const getPendingOrdersCount = useCallback((): number => {
    return orders.filter(order => order.status === 'pending').length;
  }, [orders]);

  /**
   * Get completed orders count
   */
  const getCompletedOrdersCount = useCallback((): number => {
    return orders.filter(order => order.status === 'completed').length;
  }, [orders]);

  // Global orders listener for real-time sync
  useEffect(() => {
    const unsubscribe = addGlobalOrdersListener((newOrders) => {
      if (__DEV__) console.log('🔄 useOrders received global orders update');
      setOrders(newOrders);
    });

    return unsubscribe;
  }, []);

  // Load orders on mount
  useEffect(() => {
    loadOrders();

    // Set up periodic refresh for real-time sync (every 30 seconds)
    const interval = setInterval(() => {
      if (__DEV__) console.log('🔄 Periodic orders refresh');
      loadOrders();
    }, 30000);

    return () => clearInterval(interval);
  }, [loadOrders]);

  // Debug orders state changes (only in development)
  useEffect(() => {
    if (__DEV__) console.log('🔄 useOrders state changed:', { 
      count: orders.length, 
      loading, 
      error 
    });
  }, [orders, loading, error]);

  return {
    orders,
    loading,
    error,
    addOrder,
    updateOrderStatus,
    deleteOrder,
    getOrdersByStatus,
    searchOrders,
    refreshOrders,
    getOrderById,
    getPendingOrdersCount,
    getCompletedOrdersCount,
  };
}
